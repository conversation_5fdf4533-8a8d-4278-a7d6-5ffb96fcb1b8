TRANSFERT {INIT 19x19; EDIT 1 [0,0]; EDIT 1 [0,1]; EDIT 1 [0,2]; EDIT 1 [0,3]; EDIT 1 [0,4]; EDIT 1 [0,5]; EDIT 1 [0,6]; EDIT 1 [0,7]; EDIT 1 [0,8]; EDIT 1 [0,9]; EDIT 1 [0,10]; EDIT 1 [0,11]; EDIT 1 [0,12]; EDIT 4 [0,13]; EDIT 1 [0,14]; EDIT 1 [0,15]; EDIT 1 [0,16]; EDIT 1 [0,17]; EDIT 1 [0,18]; EDIT 1 [1,0]; EDIT 1 [1,1]; EDIT 1 [1,2]; EDIT 1 [1,3]; EDIT 1 [1,4]; EDIT 1 [1,5]; EDIT 1 [1,6]; EDIT 1 [1,7]; EDIT 1 [1,8]; EDIT 1 [1,9]; EDIT 1 [1,10]; EDIT 1 [1,11]; EDIT 1 [1,12]; EDIT 1 [1,13]; EDIT 1 [1,14]; EDIT 1 [1,15]; EDIT 1 [1,16]; EDIT 1 [1,17]; EDIT 1 [1,18]; EDIT 1 [2,0]; EDIT 1 [2,1]; EDIT 1 [2,2]; EDIT 1 [2,3]; EDIT 1 [2,4]; EDIT 1 [2,5]; EDIT 1 [2,6]; EDIT 1 [2,7]; EDIT 1 [2,8]; EDIT 1 [2,9]; EDIT 1 [2,10]; EDIT 1 [2,11]; EDIT 1 [2,12]; EDIT 4 [2,13]; EDIT 1 [2,14]; EDIT 1 [2,15]; EDIT 1 [2,16]; EDIT 1 [2,17]; EDIT 1 [2,18]; EDIT 1 [3,0]; EDIT 1 [3,1]; EDIT 1 [3,2]; EDIT 1 [3,3]; EDIT 1 [3,4]; EDIT 1 [3,5]; EDIT 1 [3,6]; EDIT 8 [3,7]; EDIT 8 [3,8]; EDIT 8 [3,9]; EDIT 1 [3,10]; EDIT 1 [3,11]; EDIT 1 [3,12]; EDIT 4 [3,13]; EDIT 1 [3,14]; EDIT 1 [3,15]; EDIT 1 [3,16]; EDIT 1 [3,17]; EDIT 1 [3,18]; EDIT 1 [4,0]; EDIT 1 [4,1]; EDIT 1 [4,2]; EDIT 1 [4,3]; EDIT 1 [4,4]; EDIT 1 [4,5]; EDIT 1 [4,6]; EDIT 8 [4,7]; EDIT 1 [4,8]; EDIT 8 [4,9]; EDIT 1 [4,10]; EDIT 1 [4,11]; EDIT 1 [4,12]; EDIT 4 [4,13]; EDIT 1 [4,14]; EDIT 1 [4,15]; EDIT 1 [4,16]; EDIT 1 [4,17]; EDIT 1 [4,18]; EDIT 1 [5,0]; EDIT 1 [5,1]; EDIT 1 [5,2]; EDIT 1 [5,3]; EDIT 1 [5,4]; EDIT 1 [5,5]; EDIT 1 [5,6]; EDIT 8 [5,7]; EDIT 8 [5,8]; EDIT 8 [5,9]; EDIT 1 [5,10]; EDIT 1 [5,11]; EDIT 1 [5,12]; EDIT 4 [5,13]; EDIT 1 [5,14]; EDIT 1 [5,15]; EDIT 1 [5,16]; EDIT 1 [5,17]; EDIT 1 [5,18]; EDIT 1 [6,0]; EDIT 1 [6,1]; EDIT 1 [6,2]; EDIT 1 [6,3]; EDIT 1 [6,4]; EDIT 1 [6,5]; EDIT 1 [6,6]; EDIT 1 [6,7]; EDIT 1 [6,8]; EDIT 1 [6,9]; EDIT 1 [6,10]; EDIT 1 [6,11]; EDIT 1 [6,12]; EDIT 4 [6,13]; EDIT 4 [6,14]; EDIT 4 [6,15]; EDIT 4 [6,16]; EDIT 4 [6,17]; EDIT 1 [6,18]; EDIT 1 [7,0]; EDIT 1 [7,1]; EDIT 1 [7,2]; EDIT 1 [7,3]; EDIT 1 [7,4]; EDIT 1 [7,5]; EDIT 1 [7,6]; EDIT 1 [7,7]; EDIT 1 [7,8]; EDIT 1 [7,9]; EDIT 1 [7,10]; EDIT 1 [7,11]; EDIT 1 [7,12]; EDIT 1 [7,13]; EDIT 1 [7,14]; EDIT 1 [7,15]; EDIT 1 [7,16]; EDIT 1 [7,17]; EDIT 1 [7,18]; EDIT 1 [8,0]; EDIT 1 [8,1]; EDIT 1 [8,2]; EDIT 3 [8,3]; EDIT 3 [8,4]; EDIT 1 [8,5]; EDIT 3 [8,6]; EDIT 3 [8,7]; EDIT 1 [8,8]; EDIT 1 [8,9]; EDIT 1 [8,10]; EDIT 1 [8,11]; EDIT 1 [8,12]; EDIT 1 [8,13]; EDIT 1 [8,14]; EDIT 1 [8,15]; EDIT 1 [8,16]; EDIT 1 [8,17]; EDIT 1 [8,18]; EDIT 1 [9,0]; EDIT 1 [9,1]; EDIT 1 [9,2]; EDIT 3 [9,3]; EDIT 1 [9,4]; EDIT 1 [9,5]; EDIT 1 [9,6]; EDIT 3 [9,7]; EDIT 1 [9,8]; EDIT 1 [9,9]; EDIT 1 [9,10]; EDIT 1 [9,11]; EDIT 1 [9,12]; EDIT 1 [9,13]; EDIT 1 [9,14]; EDIT 1 [9,15]; EDIT 1 [9,16]; EDIT 1 [9,17]; EDIT 1 [9,18]; EDIT 1 [10,0]; EDIT 1 [10,1]; EDIT 1 [10,2]; EDIT 1 [10,3]; EDIT 1 [10,4]; EDIT 1 [10,5]; EDIT 1 [10,6]; EDIT 1 [10,7]; EDIT 1 [10,8]; EDIT 1 [10,9]; EDIT 1 [10,10]; EDIT 1 [10,11]; EDIT 1 [10,12]; EDIT 1 [10,13]; EDIT 1 [10,14]; EDIT 1 [10,15]; EDIT 1 [10,16]; EDIT 1 [10,17]; EDIT 1 [10,18]; EDIT 1 [11,0]; EDIT 1 [11,1]; EDIT 1 [11,2]; EDIT 3 [11,3]; EDIT 1 [11,4]; EDIT 1 [11,5]; EDIT 1 [11,6]; EDIT 3 [11,7]; EDIT 1 [11,8]; EDIT 1 [11,9]; EDIT 1 [11,10]; EDIT 1 [11,11]; EDIT 1 [11,12]; EDIT 1 [11,13]; EDIT 1 [11,14]; EDIT 1 [11,15]; EDIT 1 [11,16]; EDIT 1 [11,17]; EDIT 1 [11,18]; EDIT 1 [12,0]; EDIT 1 [12,1]; EDIT 1 [12,2]; EDIT 3 [12,3]; EDIT 3 [12,4]; EDIT 1 [12,5]; EDIT 3 [12,6]; EDIT 3 [12,7]; EDIT 1 [12,8]; EDIT 1 [12,9]; EDIT 1 [12,10]; EDIT 1 [12,11]; EDIT 1 [12,12]; EDIT 1 [12,13]; EDIT 1 [12,14]; EDIT 1 [12,15]; EDIT 1 [12,16]; EDIT 1 [12,17]; EDIT 1 [12,18]; EDIT 1 [13,0]; EDIT 1 [13,1]; EDIT 1 [13,2]; EDIT 1 [13,3]; EDIT 1 [13,4]; EDIT 1 [13,5]; EDIT 1 [13,6]; EDIT 1 [13,7]; EDIT 1 [13,8]; EDIT 1 [13,9]; EDIT 1 [13,10]; EDIT 1 [13,11]; EDIT 1 [13,12]; EDIT 1 [13,13]; EDIT 6 [13,14]; EDIT 6 [13,15]; EDIT 1 [13,16]; EDIT 1 [13,17]; EDIT 1 [13,18]; EDIT 1 [14,0]; EDIT 1 [14,1]; EDIT 1 [14,2]; EDIT 1 [14,3]; EDIT 1 [14,4]; EDIT 1 [14,5]; EDIT 1 [14,6]; EDIT 1 [14,7]; EDIT 1 [14,8]; EDIT 1 [14,9]; EDIT 1 [14,10]; EDIT 1 [14,11]; EDIT 1 [14,12]; EDIT 1 [14,13]; EDIT 6 [14,14]; EDIT 1 [14,15]; EDIT 1 [14,16]; EDIT 1 [14,17]; EDIT 1 [14,18]; EDIT 1 [15,0]; EDIT 1 [15,1]; EDIT 1 [15,2]; EDIT 1 [15,3]; EDIT 2 [15,4]; EDIT 2 [15,5]; EDIT 2 [15,6]; EDIT 1 [15,7]; EDIT 1 [15,8]; EDIT 1 [15,9]; EDIT 2 [15,10]; EDIT 2 [15,11]; EDIT 2 [15,12]; EDIT 1 [15,13]; EDIT 1 [15,14]; EDIT 1 [15,15]; EDIT 1 [15,16]; EDIT 1 [15,17]; EDIT 1 [15,18]; EDIT 1 [16,0]; EDIT 1 [16,1]; EDIT 1 [16,2]; EDIT 1 [16,3]; EDIT 2 [16,4]; EDIT 1 [16,5]; EDIT 1 [16,6]; EDIT 1 [16,7]; EDIT 1 [16,8]; EDIT 1 [16,9]; EDIT 1 [16,10]; EDIT 1 [16,11]; EDIT 2 [16,12]; EDIT 1 [16,13]; EDIT 1 [16,14]; EDIT 1 [16,15]; EDIT 1 [16,16]; EDIT 1 [16,17]; EDIT 1 [16,18]; EDIT 1 [17,0]; EDIT 1 [17,1]; EDIT 1 [17,2]; EDIT 1 [17,3]; EDIT 2 [17,4]; EDIT 1 [17,5]; EDIT 1 [17,6]; EDIT 1 [17,7]; EDIT 1 [17,8]; EDIT 1 [17,9]; EDIT 1 [17,10]; EDIT 1 [17,11]; EDIT 2 [17,12]; EDIT 1 [17,13]; EDIT 1 [17,14]; EDIT 1 [17,15]; EDIT 1 [17,16]; EDIT 1 [17,17]; EDIT 1 [17,18]; EDIT 1 [18,0]; EDIT 1 [18,1]; EDIT 1 [18,2]; EDIT 1 [18,3]; EDIT 1 [18,4]; EDIT 1 [18,5]; EDIT 1 [18,6]; EDIT 1 [18,7]; EDIT 1 [18,8]; EDIT 1 [18,9]; EDIT 1 [18,10]; EDIT 1 [18,11]; EDIT 1 [18,12]; EDIT 1 [18,13]; EDIT 6 [18,14]; EDIT 1 [18,15]; EDIT 1 [18,16]; EDIT 1 [18,17]; EDIT 1 [18,18]}
CUT (COLOR 8 [3,7 5,9])
PASTE [9,4]
CUT (COLOR 6 [13,14 18,15])
PASTE [7,2]
EDIT 6 ([13,2] [13,3] [13,7] [13,8] [12,8] [8,8] [7,8] [7,7])
CUT (COLOR 2 [15,4 17,12])
PASTE [6,1]
FILL 2 ([12,1 14,1] [14,2 14,3] [14,7 14,8] [12,9 14,9])
CUT (COLOR 4 [0,13 6,17])
PASTE [9,0]
FILL 4 ([5,0 8,0] [5,1 5,4] [5,10 5,6] [6,10 9,10] [11,10 15,10] [15,6 15,9])
EDIT 1 [15,5]
EXTRACT [5,0 15,10]
END