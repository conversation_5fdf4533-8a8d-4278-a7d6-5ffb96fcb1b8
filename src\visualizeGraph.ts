import fs from 'fs';
import path from 'path';

const INPUT_FILE = path.join(__dirname, '../output/knowledge-graph.json');
const OUTPUT_FILE = path.join(__dirname, '../output/knowledge-graph.html');

function generateVisualization() {
    const knowledgeGraph = JSON.parse(fs.readFileSync(INPUT_FILE, 'utf-8'));

    // Filtrage des nœuds pour une meilleure visualisation
    const semanticNodes = knowledgeGraph.nodes.filter((node: any) =>
        node.type.includes('semantic') ||
        node.type.includes('global') ||
        node.type.includes('pattern') ||
        node.type.includes('strategy') ||
        node.type.includes('complexity')
    );

    const semanticEdges = knowledgeGraph.edges.filter((edge: any) =>
        semanticNodes.some((node: any) => node.id === edge.from) &&
        semanticNodes.some((node: any) => node.id === edge.to)
    );

    // Génération des statistiques détaillées
    const insights = knowledgeGraph.metadata?.semanticInsights;

    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>ARC AGI Knowledge Graph - Semantic Analysis</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; }
        .network-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .insights-panel { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        #mynetworkid { width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 4px; }
        .stat-card { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #007bff; }
        .stat-title { font-weight: bold; color: #333; margin-bottom: 5px; }
        .stat-value { font-size: 1.2em; color: #007bff; }
        .chart-container { margin: 20px 0; }
        .chart-canvas { max-height: 300px; }
        h1 { color: #333; margin: 0; }
        h2 { color: #555; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        h3 { color: #666; }
        .correlation { background: #e8f4fd; padding: 10px; border-radius: 4px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 ARC AGI Knowledge Graph - Semantic Analysis</h1>
            <p>Advanced analysis of ${knowledgeGraph.metadata?.totalScenarios || 0} ARC AGI scenarios with semantic insights and pattern recognition.</p>
        </div>

        <div class="grid">
            <div class="network-container">
                <h2>🕸️ Semantic Network Visualization</h2>
                <div id="mynetworkid"></div>
                <p><strong>Showing:</strong> ${semanticNodes.length} semantic nodes and ${semanticEdges.length} relationships</p>
            </div>

            <div class="insights-panel">
                <h2>📊 Key Insights</h2>

                <div class="stat-card">
                    <div class="stat-title">Total Scenarios Analyzed</div>
                    <div class="stat-value">${knowledgeGraph.metadata?.totalScenarios || 0}</div>
                </div>

                <div class="stat-card">
                    <div class="stat-title">Colors in Palette</div>
                    <div class="stat-value">${insights?.colorAnalysis?.palette?.length || 0}</div>
                </div>

                <div class="stat-card">
                    <div class="stat-title">Grids Analyzed</div>
                    <div class="stat-value">${insights?.gridAnalysis?.dimensions?.length || 0}</div>
                </div>

                <div class="stat-card">
                    <div class="stat-title">Cognitive Concepts</div>
                    <div class="stat-value">${insights?.strategyAnalysis?.cognitiveLoad?.conceptsRequired?.length || 0}</div>
                </div>

                <h3>🎯 Complexity Distribution</h3>
                <div class="chart-container">
                    <canvas id="complexityChart" class="chart-canvas"></canvas>
                </div>

                <h3>🎨 Dominant Colors</h3>
                <div class="chart-container">
                    <canvas id="colorChart" class="chart-canvas"></canvas>
                </div>

                <h3>🔗 Correlations</h3>
                <div class="correlation">
                    <strong>Grid Size ↔ Complexity:</strong> ${insights?.correlations?.gridSizeVsComplexity?.toFixed(3) || 'N/A'}
                </div>
                <div class="correlation">
                    <strong>Color Count ↔ Complexity:</strong> ${insights?.correlations?.colorCountVsComplexity?.toFixed(3) || 'N/A'}
                </div>

                <h3>🧩 Solution Strategies</h3>
                ${Object.entries(insights?.strategyAnalysis?.solutionStrategies || {}).map(([strategy, count]) =>
                    `<div class="stat-card">
                        <div class="stat-title">${strategy.replace(/([A-Z])/g, ' $1').trim()}</div>
                        <div class="stat-value">${count}</div>
                    </div>`
                ).join('')}
            </div>
        </div>
    </div>

    <script>
        function getNodeColor(type) {
            const colors = {
                'semantic_analysis': '#ff6b6b',
                'global_action': '#4ecdc4',
                'global_pattern': '#45b7d1',
                'grid_category': '#96ceb4',
                'dominant_color': '#feca57',
                'geometric_pattern': '#ff9ff3',
                'solution_strategy': '#54a0ff',
                'complexity_level': '#5f27cd'
            };
            return colors[type] || '#ddd';
        }

        function getNodeSize(type) {
            const sizes = {
                'semantic_analysis': 25,
                'global_action': 20,
                'global_pattern': 20,
                'grid_category': 15,
                'dominant_color': 15,
                'geometric_pattern': 15,
                'solution_strategy': 18,
                'complexity_level': 18
            };
            return sizes[type] || 10;
        }

        // Configuration du réseau avec couleurs et tailles
        const processedNodes = ${JSON.stringify(semanticNodes)}.map(node => ({
            ...node,
            color: getNodeColor(node.type),
            size: getNodeSize(node.type)
        }));

        const nodes = new vis.DataSet(processedNodes);
        const edges = new vis.DataSet(${JSON.stringify(semanticEdges)});

        const container = document.getElementById('mynetworkid');
        const data = { nodes: nodes, edges: edges };
        const options = {
            nodes: {
                shape: 'dot',
                font: { size: 14, color: '#333' },
                borderWidth: 2,
                shadow: true
            },
            edges: {
                arrows: 'to',
                color: { color: '#848484', highlight: '#007bff' },
                width: 2,
                smooth: { type: 'continuous' }
            },
            physics: {
                enabled: true,
                stabilization: { iterations: 200 },
                barnesHut: {
                    gravitationalConstant: -8000,
                    centralGravity: 0.3,
                    springLength: 95,
                    springConstant: 0.04,
                    damping: 0.09
                }
            },
            interaction: {
                hover: true,
                tooltipDelay: 200
            }
        };

        const network = new vis.Network(container, data, options);

        // Graphiques
        const complexityData = ${JSON.stringify(insights?.strategyAnalysis?.complexityLevels || {})};
        const colorData = ${JSON.stringify(insights?.colorAnalysis?.dominantColors?.slice(0, 8) || [])};

        // Graphique de complexité
        new Chart(document.getElementById('complexityChart'), {
            type: 'doughnut',
            data: {
                labels: Object.keys(complexityData).map(k => k.charAt(0).toUpperCase() + k.slice(1)),
                datasets: [{
                    data: Object.values(complexityData),
                    backgroundColor: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom' }
                }
            }
        });

        // Graphique des couleurs
        new Chart(document.getElementById('colorChart'), {
            type: 'bar',
            data: {
                labels: colorData.map(c => \`Color \${c.color}\`),
                datasets: [{
                    label: 'Frequency',
                    data: colorData.map(c => c.frequency),
                    backgroundColor: '#007bff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });
    </script>
</body>
</html>`;

    fs.writeFileSync(OUTPUT_FILE, html);
    console.log('Enhanced visualization generated successfully and saved to knowledge-graph.html');
}

generateVisualization();
