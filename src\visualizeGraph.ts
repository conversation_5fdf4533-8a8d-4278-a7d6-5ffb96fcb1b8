import fs from 'fs';
import path from 'path';

const OUTPUT_FILE = path.join(__dirname, '../output/knowledge-graph.json');
const HTML_FILE = path.join(__dirname, '../output/knowledge-graph.html');

function generateHTML(graph: { nodes: any[]; edges: any[] }) {
    return `<!DOCTYPE html>
<html>
<head>
  <title>Knowledge Graph Visualization</title>
  <script type="text/javascript" src="https://unpkg.com/vis-network@9.1.2/dist/vis-network.min.js"></script>
  <style type="text/css">
    #network {
      width: 100vw;
      height: 90vh;
      border: 1px solid lightgray;
    }
  </style>
</head>
<body>
  <h2>Knowledge Graph Visualization</h2>
  <div id="network"></div>
  <script type="text/javascript">
    const nodes = new vis.DataSet(${JSON.stringify(graph.nodes)});
    const edges = new vis.DataSet(${JSON.stringify(graph.edges)});
    const container = document.getElementById('network');
    const data = { nodes, edges };
    const options = {
      physics: { enabled: true },
      nodes: { shape: 'dot', size: 20 },
      edges: { arrows: 'to' }
    };
    new vis.Network(container, data, options);
  </script>
</body>
</html>`;
}

function main() {
    if (!fs.existsSync(OUTPUT_FILE)) {
        console.error('Knowledge graph JSON not found. Run npm run generate first.');
        return;
    }
    const graph = JSON.parse(fs.readFileSync(OUTPUT_FILE, 'utf-8'));
    const html = generateHTML(graph);
    fs.writeFileSync(HTML_FILE, html);
    console.log('Visualization HTML generated at output/knowledge-graph.html');
}

main();
