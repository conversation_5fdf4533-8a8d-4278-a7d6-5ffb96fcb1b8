TRANSFERT {INIT 15x15; EDIT 8 [0,0]; EDIT 8 [0,1]; EDIT 8 [0,3]; EDIT 8 [0,6]; EDIT 8 [1,1]; EDIT 8 [1,3]; EDIT 8 [1,4]; EDIT 8 [1,5]; EDIT 8 [1,6]; EDIT 4 [1,7]; EDIT 8 [1,8]; EDIT 8 [1,9]; EDIT 8 [1,10]; EDIT 8 [1,11]; EDIT 8 [1,12]; EDIT 8 [1,13]; EDIT 8 [1,14]; EDIT 8 [2,1]; EDIT 4 [2,6] [2,7]; EDIT 8 [2,8]; EDIT 8 [2,14]; EDIT 8 [3,1]; EDIT 8 [3,2]; EDIT 8 [3,3]; EDIT 8 [3,4]; EDIT 8 [3,5]; EDIT 8 [3,6]; EDIT 4 [3,7]; EDIT 8 [3,8]; EDIT 8 [3,9]; EDIT 8 [3,10]; EDIT 8 [3,12]; EDIT 8 [3,13]; EDIT 8 [3,14]; EDIT 8 [4,6]; EDIT 8 [4,10]; EDIT 8 [4,12]; EDIT 8 [5,0]; EDIT 8 [5,1]; EDIT 8 [5,2]; EDIT 8 [5,3]; EDIT 8 [5,4]; EDIT 8 [5,6]; EDIT 8 [5,7]; EDIT 8 [5,8]; EDIT 8 [5,10]; EDIT 8 [5,12]; EDIT 8 [5,14]; EDIT 8 [6,4]; EDIT 8 [6,8]; EDIT 8 [6,10]; EDIT 8 [6,12]; EDIT 8 [6,14]; EDIT 8 [7,0]; EDIT 8 [7,1]; EDIT 8 [7,2]; EDIT 8 [7,4]; EDIT 8 [7,5]; EDIT 8 [7,6]; EDIT 8 [7,8]; EDIT 8 [7,10]; EDIT 8 [7,12]; EDIT 8 [7,13]; EDIT 8 [7,14]; EDIT 8 [8,2]; EDIT 8 [8,6]; EDIT 8 [8,8]; EDIT 8 [8,10]; EDIT 8 [9,0]; EDIT 8 [9,2]; EDIT 8 [9,3]; EDIT 8 [9,4]; EDIT 8 [9,6]; EDIT 8 [9,7]; EDIT 8 [9,8]; EDIT 8 [9,10]; EDIT 8 [9,11]; EDIT 8 [9,12]; EDIT 8 [9,14]; EDIT 8 [10,0]; EDIT 8 [10,4]; EDIT 8 [10,12]; EDIT 8 [10,14]; EDIT 8 [11,0]; EDIT 8 [11,1]; EDIT 8 [11,2]; EDIT 8 [11,4]; EDIT 8 [11,6]; EDIT 8 [11,7]; EDIT 8 [11,8]; EDIT 8 [11,9]; EDIT 8 [11,10]; EDIT 8 [11,11]; EDIT 8 [11,12]; EDIT 8 [11,14]; EDIT 8 [12,2]; EDIT 8 [12,4]; EDIT 8 [12,6]; EDIT 8 [12,14]; EDIT 8 [13,0]; EDIT 8 [13,2]; EDIT 8 [13,3]; EDIT 8 [13,4]; EDIT 8 [13,6]; EDIT 8 [13,8]; EDIT 8 [13,9]; EDIT 8 [13,10]; EDIT 8 [13,11]; EDIT 8 [13,12]; EDIT 8 [13,13]; EDIT 8 [13,14]; EDIT 8 [14,0]; EDIT 8 [14,6]; EDIT 8 [14,8]}
FLOODFILL 4 ([6,9] [0,8] [2,3])
EDIT 3 ([0,7] [0,9] [0,11] [0,13] [4,7] [4,9] [6,9] [8,9] [10,9] [10,11] [10,7] [10,5] [8,5] [8,3] [6,3] [6,1] [12,5] [14,5] [14,3] [14,1] [12,1] [2,7] [2,5] [2,3] [1,2])
END