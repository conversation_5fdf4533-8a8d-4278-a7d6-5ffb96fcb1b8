TRANSFERT {INIT 16x17; EDIT 8 [0,3]; EDIT 1 [0,4]; EDIT 1 [0,5]; EDIT 8 [0,6]; EDIT 8 [0,9]; EDIT 8 [0,11]; EDIT 8 [0,15]; EDIT 1 [1,1]; EDIT 8 [1,3]; EDIT 8 [1,4]; EDIT 1 [1,5]; EDIT 1 [1,7]; EDIT 1 [1,8]; EDIT 2 [1,9]; EDIT 8 [1,10]; EDIT 1 [1,11]; EDIT 1 [1,12]; EDIT 2 [1,13]; EDIT 2 [1,15]; EDIT 8 [2,2]; EDIT 8 [2,3]; EDIT 1 [2,4]; EDIT 1 [2,5]; EDIT 8 [2,6]; EDIT 8 [2,7]; EDIT 1 [2,8]; EDIT 1 [2,9]; EDIT 8 [2,10]; EDIT 8 [2,12]; EDIT 1 [2,15]; EDIT 1 [3,0]; EDIT 1 [3,2]; EDIT 8 [3,4]; EDIT 1 [3,6]; EDIT 8 [3,7]; EDIT 1 [3,8]; EDIT 1 [3,10]; EDIT 1 [3,11]; EDIT 8 [3,12]; EDIT 8 [3,13]; EDIT 8 [3,14]; EDIT 8 [4,0]; EDIT 8 [4,2]; EDIT 3 [4,3]; EDIT 3 [4,4]; EDIT 3 [4,5]; EDIT 3 [4,6]; EDIT 3 [4,7]; EDIT 3 [4,8]; EDIT 3 [4,9]; EDIT 3 [4,10]; EDIT 3 [4,11]; EDIT 3 [4,12]; EDIT 3 [4,13]; EDIT 2 [4,15]; EDIT 1 [5,0]; EDIT 8 [5,2]; EDIT 3 [5,3]; EDIT 2 [5,4]; EDIT 8 [5,6]; EDIT 1 [5,7]; EDIT 1 [5,8]; EDIT 1 [5,9]; EDIT 1 [5,11]; EDIT 3 [5,13]; EDIT 8 [6,1]; EDIT 8 [6,2]; EDIT 3 [6,3]; EDIT 8 [6,4]; EDIT 1 [6,5]; EDIT 8 [6,7]; EDIT 2 [6,8]; EDIT 8 [6,9]; EDIT 1 [6,10]; EDIT 2 [6,11]; EDIT 8 [6,12]; EDIT 3 [6,13]; EDIT 1 [6,14]; EDIT 8 [6,15]; EDIT 1 [7,0]; EDIT 8 [7,2]; EDIT 3 [7,3]; EDIT 8 [7,4]; EDIT 2 [7,5]; EDIT 2 [7,7]; EDIT 1 [7,9]; EDIT 1 [7,10]; EDIT 8 [7,11]; EDIT 1 [7,12]; EDIT 3 [7,13]; EDIT 8 [7,14]; EDIT 8 [7,15]; EDIT 8 [8,1]; EDIT 3 [8,3]; EDIT 1 [8,5]; EDIT 8 [8,6]; EDIT 8 [8,7]; EDIT 1 [8,8]; EDIT 1 [8,9]; EDIT 8 [8,10]; EDIT 1 [8,11]; EDIT 8 [8,12]; EDIT 3 [8,13]; EDIT 2 [8,14]; EDIT 1 [8,15]; EDIT 1 [9,0]; EDIT 3 [9,3]; EDIT 1 [9,5]; EDIT 8 [9,6]; EDIT 8 [9,7]; EDIT 8 [9,9]; EDIT 2 [9,11]; EDIT 3 [9,13]; EDIT 8 [9,14]; EDIT 1 [9,15]; EDIT 8 [10,1]; EDIT 8 [10,2]; EDIT 3 [10,3]; EDIT 8 [10,5]; EDIT 8 [10,6]; EDIT 2 [10,7]; EDIT 8 [10,8]; EDIT 8 [10,9]; EDIT 8 [10,10]; EDIT 8 [10,11]; EDIT 8 [10,12]; EDIT 3 [10,13]; EDIT 8 [10,14]; EDIT 8 [10,15]; EDIT 1 [11,0]; EDIT 1 [11,1]; EDIT 1 [11,2]; EDIT 3 [11,3]; EDIT 8 [11,4]; EDIT 2 [11,6]; EDIT 8 [11,11]; EDIT 8 [11,12]; EDIT 3 [11,13]; EDIT 8 [11,14]; EDIT 1 [12,0]; EDIT 8 [12,1]; EDIT 3 [12,3]; EDIT 2 [12,5]; EDIT 8 [12,6]; EDIT 8 [12,7]; EDIT 1 [12,8]; EDIT 2 [12,9]; EDIT 2 [12,12]; EDIT 3 [12,13]; EDIT 8 [12,14]; EDIT 1 [12,15]; EDIT 8 [13,0]; EDIT 1 [13,1]; EDIT 1 [13,2]; EDIT 3 [13,3]; EDIT 3 [13,4]; EDIT 3 [13,5]; EDIT 3 [13,6]; EDIT 3 [13,7]; EDIT 3 [13,8]; EDIT 3 [13,9]; EDIT 3 [13,10]; EDIT 3 [13,11]; EDIT 3 [13,12]; EDIT 3 [13,13]; EDIT 2 [13,15]; EDIT 8 [14,0]; EDIT 1 [14,1]; EDIT 8 [14,6]; EDIT 8 [14,7]; EDIT 1 [14,9]; EDIT 2 [14,10]; EDIT 8 [14,11]; EDIT 8 [14,12]; EDIT 8 [14,13]; EDIT 1 [14,14]; EDIT 8 [14,15]; EDIT 8 [15,0]; EDIT 1 [15,1]; EDIT 1 [15,4]; EDIT 1 [15,5]; EDIT 8 [15,6]; EDIT 1 [15,8]; EDIT 2 [15,9]; EDIT 8 [15,10]; EDIT 1 [15,11]; EDIT 1 [15,13]; EDIT 2 [15,14]; EDIT 8 [16,0]; EDIT 8 [16,2]; EDIT 2 [16,3]; EDIT 8 [16,4]; EDIT 8 [16,6]; EDIT 2 [16,7]; EDIT 1 [16,9]; EDIT 8 [16,10]; EDIT 1 [16,11]; EDIT 8 [16,12]; EDIT 1 [16,13]; EDIT 8 [16,14]; EDIT 8 [16,15]}
EXTRACT [5,4 12,12]
END