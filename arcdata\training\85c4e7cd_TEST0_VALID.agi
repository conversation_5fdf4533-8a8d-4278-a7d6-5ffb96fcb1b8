TRANSFERT {INIT 14x14; EDIT 8 [0,0]; EDIT 8 [0,1]; EDIT 8 [0,2]; EDIT 8 [0,3]; EDIT 8 [0,4]; EDIT 8 [0,5]; EDIT 8 [0,6]; EDIT 8 [0,7]; EDIT 8 [0,8]; EDIT 8 [0,9]; EDIT 8 [0,10]; EDIT 8 [0,11]; EDIT 8 [0,12]; EDIT 8 [0,13]; EDIT 8 [1,0]; EDIT 2 [1,1]; EDIT 2 [1,2]; EDIT 2 [1,3]; EDIT 2 [1,4]; EDIT 2 [1,5]; EDIT 2 [1,6]; EDIT 2 [1,7]; EDIT 2 [1,8]; EDIT 2 [1,9]; EDIT 2 [1,10]; EDIT 2 [1,11]; EDIT 2 [1,12]; EDIT 8 [1,13]; EDIT 8 [2,0]; EDIT 2 [2,1]; EDIT 4 [2,2]; EDIT 4 [2,3]; EDIT 4 [2,4]; EDIT 4 [2,5]; EDIT 4 [2,6]; EDIT 4 [2,7]; EDIT 4 [2,8]; EDIT 4 [2,9]; EDIT 4 [2,10]; EDIT 4 [2,11]; EDIT 2 [2,12]; EDIT 8 [2,13]; EDIT 8 [3,0]; EDIT 2 [3,1]; EDIT 4 [3,2]; EDIT 3 [3,3]; EDIT 3 [3,4]; EDIT 3 [3,5]; EDIT 3 [3,6]; EDIT 3 [3,7]; EDIT 3 [3,8]; EDIT 3 [3,9]; EDIT 3 [3,10]; EDIT 4 [3,11]; EDIT 2 [3,12]; EDIT 8 [3,13]; EDIT 8 [4,0]; EDIT 2 [4,1]; EDIT 4 [4,2]; EDIT 3 [4,3]; EDIT 7 [4,4]; EDIT 7 [4,5]; EDIT 7 [4,6]; EDIT 7 [4,7]; EDIT 7 [4,8]; EDIT 7 [4,9]; EDIT 3 [4,10]; EDIT 4 [4,11]; EDIT 2 [4,12]; EDIT 8 [4,13]; EDIT 8 [5,0]; EDIT 2 [5,1]; EDIT 4 [5,2]; EDIT 3 [5,3]; EDIT 7 [5,4]; EDIT 6 [5,5]; EDIT 6 [5,6]; EDIT 6 [5,7]; EDIT 6 [5,8]; EDIT 7 [5,9]; EDIT 3 [5,10]; EDIT 4 [5,11]; EDIT 2 [5,12]; EDIT 8 [5,13]; EDIT 8 [6,0]; EDIT 2 [6,1]; EDIT 4 [6,2]; EDIT 3 [6,3]; EDIT 7 [6,4]; EDIT 6 [6,5]; EDIT 5 [6,6]; EDIT 5 [6,7]; EDIT 6 [6,8]; EDIT 7 [6,9]; EDIT 3 [6,10]; EDIT 4 [6,11]; EDIT 2 [6,12]; EDIT 8 [6,13]; EDIT 8 [7,0]; EDIT 2 [7,1]; EDIT 4 [7,2]; EDIT 3 [7,3]; EDIT 7 [7,4]; EDIT 6 [7,5]; EDIT 5 [7,6]; EDIT 5 [7,7]; EDIT 6 [7,8]; EDIT 7 [7,9]; EDIT 3 [7,10]; EDIT 4 [7,11]; EDIT 2 [7,12]; EDIT 8 [7,13]; EDIT 8 [8,0]; EDIT 2 [8,1]; EDIT 4 [8,2]; EDIT 3 [8,3]; EDIT 7 [8,4]; EDIT 6 [8,5]; EDIT 6 [8,6]; EDIT 6 [8,7]; EDIT 6 [8,8]; EDIT 7 [8,9]; EDIT 3 [8,10]; EDIT 4 [8,11]; EDIT 2 [8,12]; EDIT 8 [8,13]; EDIT 8 [9,0]; EDIT 2 [9,1]; EDIT 4 [9,2]; EDIT 3 [9,3]; EDIT 7 [9,4]; EDIT 7 [9,5]; EDIT 7 [9,6]; EDIT 7 [9,7]; EDIT 7 [9,8]; EDIT 7 [9,9]; EDIT 3 [9,10]; EDIT 4 [9,11]; EDIT 2 [9,12]; EDIT 8 [9,13]; EDIT 8 [10,0]; EDIT 2 [10,1]; EDIT 4 [10,2]; EDIT 3 [10,3]; EDIT 3 [10,4]; EDIT 3 [10,5]; EDIT 3 [10,6]; EDIT 3 [10,7]; EDIT 3 [10,8]; EDIT 3 [10,9]; EDIT 3 [10,10]; EDIT 4 [10,11]; EDIT 2 [10,12]; EDIT 8 [10,13]; EDIT 8 [11,0]; EDIT 2 [11,1]; EDIT 4 [11,2]; EDIT 4 [11,3]; EDIT 4 [11,4]; EDIT 4 [11,5]; EDIT 4 [11,6]; EDIT 4 [11,7]; EDIT 4 [11,8]; EDIT 4 [11,9]; EDIT 4 [11,10]; EDIT 4 [11,11]; EDIT 2 [11,12]; EDIT 8 [11,13]; EDIT 8 [12,0]; EDIT 2 [12,1]; EDIT 2 [12,2]; EDIT 2 [12,3]; EDIT 2 [12,4]; EDIT 2 [12,5]; EDIT 2 [12,6]; EDIT 2 [12,7]; EDIT 2 [12,8]; EDIT 2 [12,9]; EDIT 2 [12,10]; EDIT 2 [12,11]; EDIT 2 [12,12]; EDIT 8 [12,13]; EDIT 8 [13,0]; EDIT 8 [13,1]; EDIT 8 [13,2]; EDIT 8 [13,3]; EDIT 8 [13,4]; EDIT 8 [13,5]; EDIT 8 [13,6]; EDIT 8 [13,7]; EDIT 8 [13,8]; EDIT 8 [13,9]; EDIT 8 [13,10]; EDIT 8 [13,11]; EDIT 8 [13,12]; EDIT 8 [13,13]}
FLOODFILLS {FLOODFILL 8 [6,6]; FLOODFILL 2 [5,7]; FLOODFILL 4 [4,7]; FLOODFILL 5 [0,9]; FLOODFILL 6 [1,8]; FLOODFILL 7 [2,8]}
END