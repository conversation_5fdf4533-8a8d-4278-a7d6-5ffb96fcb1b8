// Détecteur de patterns avancé pour les scénarios ARC AGI
import { ScenarioCommand, ScenarioMetadata, Coord, CoordRect, Selection } from './parseScenarioAGI';

export interface PatternSignature {
  id: string;
  name: string;
  description: string;
  frequency: number;
  examples: string[];
  complexity: number;
  cognitiveLoad: number;
}

export interface SequencePattern {
  pattern: string[];
  frequency: number;
  avgComplexity: number;
  scenarios: string[];
}

export interface SpatialPattern {
  type: 'line' | 'rectangle' | 'cross' | 'diagonal' | 'cluster' | 'grid';
  frequency: number;
  avgSize: number;
  scenarios: string[];
}

export interface TransformationPattern {
  type: 'translation' | 'rotation' | 'reflection' | 'scaling' | 'color_change' | 'shape_change';
  frequency: number;
  parameters: any;
  scenarios: string[];
}

export interface ProblemCategory {
  category: string;
  description: string;
  patterns: string[];
  frequency: number;
  avgComplexity: number;
  solutionStrategies: string[];
}

export interface PatternAnalysisResult {
  sequencePatterns: SequencePattern[];
  spatialPatterns: SpatialPattern[];
  transformationPatterns: TransformationPattern[];
  problemCategories: ProblemCategory[];
  patternSignatures: PatternSignature[];
  insights: {
    mostCommonPattern: string;
    complexityTrends: { [pattern: string]: number };
    strategicRecommendations: string[];
  };
}

export class AdvancedPatternDetector {
  
  // Détecte les patterns de séquences d'actions
  detectSequencePatterns(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): SequencePattern[] {
    const sequenceMap = new Map<string, { count: number; complexity: number[]; scenarios: string[] }>();
    
    scenarios.forEach(scenario => {
      const actions = scenario.commands.map(cmd => cmd.action);
      const fileName = scenario.file || 'unknown';
      
      // Détection de patterns de longueur 2-4
      for (let len = 2; len <= Math.min(4, actions.length); len++) {
        for (let i = 0; i <= actions.length - len; i++) {
          const pattern = actions.slice(i, i + len);
          const key = pattern.join('-');
          
          if (!sequenceMap.has(key)) {
            sequenceMap.set(key, { count: 0, complexity: [], scenarios: [] });
          }
          
          const entry = sequenceMap.get(key)!;
          entry.count++;
          entry.complexity.push(scenario.metadata.complexity);
          if (!entry.scenarios.includes(fileName)) {
            entry.scenarios.push(fileName);
          }
        }
      }
    });
    
    return Array.from(sequenceMap.entries())
      .filter(([_, data]) => data.count >= 3) // Minimum 3 occurrences
      .map(([pattern, data]) => ({
        pattern: pattern.split('-'),
        frequency: data.count,
        avgComplexity: data.complexity.reduce((a, b) => a + b, 0) / data.complexity.length,
        scenarios: data.scenarios
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }
  
  // Détecte les patterns spatiaux dans les sélections
  detectSpatialPatterns(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): SpatialPattern[] {
    const spatialMap = new Map<string, { count: number; sizes: number[]; scenarios: string[] }>();
    
    scenarios.forEach(scenario => {
      const fileName = scenario.file || 'unknown';
      
      scenario.commands.forEach(cmd => {
        if (cmd.selections) {
          const spatialType = this.classifySpatialPattern(cmd.selections);
          if (spatialType) {
            if (!spatialMap.has(spatialType.type)) {
              spatialMap.set(spatialType.type, { count: 0, sizes: [], scenarios: [] });
            }
            
            const entry = spatialMap.get(spatialType.type)!;
            entry.count++;
            entry.sizes.push(spatialType.size);
            if (!entry.scenarios.includes(fileName)) {
              entry.scenarios.push(fileName);
            }
          }
        }
      });
    });
    
    return Array.from(spatialMap.entries())
      .map(([type, data]) => ({
        type: type as SpatialPattern['type'],
        frequency: data.count,
        avgSize: data.sizes.reduce((a, b) => a + b, 0) / data.sizes.length,
        scenarios: data.scenarios
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }
  
  // Détecte les patterns de transformation
  detectTransformationPatterns(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): TransformationPattern[] {
    const transformationMap = new Map<string, { count: number; parameters: any[]; scenarios: string[] }>();
    
    scenarios.forEach(scenario => {
      const fileName = scenario.file || 'unknown';
      
      scenario.commands.forEach(cmd => {
        const transformationType = this.classifyTransformation(cmd);
        if (transformationType) {
          if (!transformationMap.has(transformationType.type)) {
            transformationMap.set(transformationType.type, { count: 0, parameters: [], scenarios: [] });
          }
          
          const entry = transformationMap.get(transformationType.type)!;
          entry.count++;
          entry.parameters.push(transformationType.parameters);
          if (!entry.scenarios.includes(fileName)) {
            entry.scenarios.push(fileName);
          }
        }
      });
    });
    
    return Array.from(transformationMap.entries())
      .map(([type, data]) => ({
        type: type as TransformationPattern['type'],
        frequency: data.count,
        parameters: this.aggregateParameters(data.parameters),
        scenarios: data.scenarios
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }
  
  // Classifie les problèmes en catégories
  categorizeProblemTypes(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): ProblemCategory[] {
    const categories = new Map<string, { patterns: Set<string>; complexity: number[]; strategies: Set<string>; scenarios: string[] }>();
    
    scenarios.forEach(scenario => {
      const fileName = scenario.file || 'unknown';
      const category = this.determineProblemCategory(scenario);
      
      if (!categories.has(category.name)) {
        categories.set(category.name, { 
          patterns: new Set(), 
          complexity: [], 
          strategies: new Set(), 
          scenarios: [] 
        });
      }
      
      const entry = categories.get(category.name)!;
      category.patterns.forEach(p => entry.patterns.add(p));
      entry.complexity.push(scenario.metadata.complexity);
      category.strategies.forEach(s => entry.strategies.add(s));
      entry.scenarios.push(fileName);
    });
    
    return Array.from(categories.entries())
      .map(([name, data]) => ({
        category: name,
        description: this.getCategoryDescription(name),
        patterns: Array.from(data.patterns),
        frequency: data.scenarios.length,
        avgComplexity: data.complexity.reduce((a, b) => a + b, 0) / data.complexity.length,
        solutionStrategies: Array.from(data.strategies)
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }
  
  // Génère des signatures de patterns uniques
  generatePatternSignatures(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): PatternSignature[] {
    const signatures: PatternSignature[] = [];
    
    // Signature basée sur la séquence d'actions
    const actionSequences = scenarios.map(s => s.commands.map(c => c.action).join('-'));
    const uniqueSequences = [...new Set(actionSequences)];
    
    uniqueSequences.forEach(sequence => {
      const matchingScenarios = scenarios.filter(s => 
        s.commands.map(c => c.action).join('-') === sequence
      );
      
      if (matchingScenarios.length >= 2) {
        const avgComplexity = matchingScenarios.reduce((sum, s) => sum + s.metadata.complexity, 0) / matchingScenarios.length;
        
        signatures.push({
          id: `seq-${sequence.replace(/[^a-zA-Z0-9]/g, '-')}`,
          name: `Action Sequence: ${sequence}`,
          description: `Pattern of actions: ${sequence.split('-').join(' → ')}`,
          frequency: matchingScenarios.length,
          examples: matchingScenarios.slice(0, 3).map(s => s.file || 'unknown'),
          complexity: avgComplexity,
          cognitiveLoad: this.calculateCognitiveLoad(sequence.split('-'))
        });
      }
    });
    
    return signatures.sort((a, b) => b.frequency - a.frequency);
  }
  
  // Analyse complète des patterns
  analyzePatterns(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): PatternAnalysisResult {
    const sequencePatterns = this.detectSequencePatterns(scenarios);
    const spatialPatterns = this.detectSpatialPatterns(scenarios);
    const transformationPatterns = this.detectTransformationPatterns(scenarios);
    const problemCategories = this.categorizeProblemTypes(scenarios);
    const patternSignatures = this.generatePatternSignatures(scenarios);
    
    // Génération d'insights
    const mostCommonPattern = sequencePatterns[0]?.pattern.join(' → ') || 'No common pattern';
    
    const complexityTrends: { [pattern: string]: number } = {};
    sequencePatterns.forEach(sp => {
      complexityTrends[sp.pattern.join('-')] = sp.avgComplexity;
    });
    
    const strategicRecommendations = this.generateStrategicRecommendations(
      sequencePatterns, spatialPatterns, transformationPatterns, problemCategories
    );
    
    return {
      sequencePatterns,
      spatialPatterns,
      transformationPatterns,
      problemCategories,
      patternSignatures,
      insights: {
        mostCommonPattern,
        complexityTrends,
        strategicRecommendations
      }
    };
  }
  
  // Méthodes utilitaires privées
  private classifySpatialPattern(selections: Selection[]): { type: string; size: number } | null {
    if (selections.length === 0) return null;
    
    if (selections.length === 1) {
      const sel = selections[0];
      if ('from' in sel && 'to' in sel) {
        const width = Math.abs(sel.to.col - sel.from.col) + 1;
        const height = Math.abs(sel.to.row - sel.from.row) + 1;
        
        if (width === 1 || height === 1) {
          return { type: 'line', size: Math.max(width, height) };
        } else {
          return { type: 'rectangle', size: width * height };
        }
      } else {
        return { type: 'point', size: 1 };
      }
    }
    
    // Analyse de patterns multiples
    if (selections.length > 1) {
      return { type: 'cluster', size: selections.length };
    }
    
    return null;
  }
  
  private classifyTransformation(cmd: ScenarioCommand): { type: string; parameters: any } | null {
    switch (cmd.action) {
      case 'COPY':
      case 'PASTE':
        return { type: 'translation', parameters: { action: cmd.action } };
      case 'ROTATE':
        return { type: 'rotation', parameters: { direction: cmd.parameters } };
      case 'FLIP':
        return { type: 'reflection', parameters: { axis: cmd.parameters } };
      case 'RESIZE':
        return { type: 'scaling', parameters: { size: cmd.parameters } };
      case 'REPLACE':
        return { type: 'color_change', parameters: { colors: cmd.parameters } };
      case 'EDIT':
        return { type: 'shape_change', parameters: { color: cmd.parameters } };
      default:
        return null;
    }
  }
  
  private determineProblemCategory(scenario: { commands: ScenarioCommand[]; metadata: ScenarioMetadata }): { name: string; patterns: string[]; strategies: string[] } {
    const actions = scenario.commands.map(c => c.action);
    const hasResize = actions.includes('RESIZE');
    const hasCopyPaste = actions.includes('COPY') && actions.includes('PASTE');
    const hasFloodFill = actions.includes('FLOODFILL');
    const hasTransform = actions.some(a => ['ROTATE', 'FLIP'].includes(a));
    
    if (hasResize) {
      return {
        name: 'Grid Transformation',
        patterns: ['resize', 'scale'],
        strategies: ['structural_modification', 'size_adaptation']
      };
    } else if (hasCopyPaste) {
      return {
        name: 'Pattern Replication',
        patterns: ['copy-paste', 'duplication'],
        strategies: ['template_matching', 'spatial_reasoning']
      };
    } else if (hasFloodFill) {
      return {
        name: 'Area Filling',
        patterns: ['flood-fill', 'region_growth'],
        strategies: ['connectivity_analysis', 'boundary_detection']
      };
    } else if (hasTransform) {
      return {
        name: 'Geometric Transformation',
        patterns: ['rotation', 'reflection'],
        strategies: ['spatial_transformation', 'symmetry_detection']
      };
    } else {
      return {
        name: 'Direct Editing',
        patterns: ['point_edit', 'local_modification'],
        strategies: ['pixel_manipulation', 'direct_drawing']
      };
    }
  }
  
  private getCategoryDescription(category: string): string {
    const descriptions: { [key: string]: string } = {
      'Grid Transformation': 'Problems involving changing the size or structure of the grid',
      'Pattern Replication': 'Problems requiring copying and pasting patterns or shapes',
      'Area Filling': 'Problems involving filling connected regions with colors',
      'Geometric Transformation': 'Problems requiring rotation, reflection, or other geometric operations',
      'Direct Editing': 'Problems solved by directly editing individual cells or small areas'
    };
    return descriptions[category] || 'Unknown problem category';
  }
  
  private aggregateParameters(parameters: any[]): any {
    // Agrégation simple des paramètres
    return {
      count: parameters.length,
      unique: [...new Set(parameters.map(p => JSON.stringify(p)))].length,
      sample: parameters[0]
    };
  }
  
  private calculateCognitiveLoad(actions: string[]): number {
    const weights: { [action: string]: number } = {
      'INIT': 1,
      'EDIT': 2,
      'COPY': 3,
      'PASTE': 3,
      'RESIZE': 4,
      'ROTATE': 5,
      'FLIP': 4,
      'FLOODFILL': 3,
      'REPLACE': 2
    };
    
    return actions.reduce((load, action) => load + (weights[action] || 2), 0);
  }
  
  private generateStrategicRecommendations(
    sequencePatterns: SequencePattern[],
    spatialPatterns: SpatialPattern[],
    transformationPatterns: TransformationPattern[],
    problemCategories: ProblemCategory[]
  ): string[] {
    const recommendations: string[] = [];
    
    if (sequencePatterns.length > 0) {
      const topPattern = sequencePatterns[0];
      recommendations.push(`Focus on mastering the "${topPattern.pattern.join(' → ')}" sequence (${topPattern.frequency} occurrences)`);
    }
    
    if (spatialPatterns.length > 0) {
      const topSpatial = spatialPatterns[0];
      recommendations.push(`Develop ${topSpatial.type} recognition skills (${topSpatial.frequency} instances)`);
    }
    
    if (problemCategories.length > 0) {
      const topCategory = problemCategories[0];
      recommendations.push(`Prioritize "${topCategory.category}" problems (${topCategory.frequency} scenarios, avg complexity: ${topCategory.avgComplexity.toFixed(1)})`);
    }
    
    return recommendations;
  }
}
