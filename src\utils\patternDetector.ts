// Analyseur d'actions pour l'étiquetage des scénarios ARC AGI
import { ScenarioCommand, ScenarioMetadata } from './parseScenarioAGI';

export interface ActionStatistics {
  action: string;
  frequency: number;
  withParameters: number;
  withoutParameters: number;
  scenarios: string[];
  parameterTypes: { [param: string]: number };
}

export interface ScenarioLabel {
  scenarioFile: string;
  primaryLabel: string;
  secondaryLabels: string[];
  actionSequence: string[];
  usesTransfert: boolean;
  usesMotif: boolean;
  usesColor: boolean;
  usesInvert: boolean;
  complexity: number;
  confidence: number;
}

export interface ActionPattern {
  pattern: string[];
  frequency: number;
  scenarios: string[];
  label: string;
}

export interface LabelingRules {
  transfertBased: string[];
  motifBased: string[];
  editingBased: string[];
  transformationBased: string[];
  fillBased: string[];
}

export interface ActionAnalysisResult {
  actionStatistics: ActionStatistics[];
  scenarioLabels: ScenarioLabel[];
  actionPatterns: ActionPattern[];
  transfertUsage: {
    total: number;
    percentage: number;
    scenarios: string[];
  };
  motifUsage: {
    total: number;
    percentage: number;
    scenarios: string[];
  };
  colorUsage: {
    total: number;
    percentage: number;
    scenarios: string[];
  };
  labelDistribution: { [label: string]: number };
  insights: {
    mostCommonLabel: string;
    mostCommonActionSequence: string[];
    transfertDominance: boolean;
    recommendations: string[];
  };
}

export class ScenarioActionAnalyzer {

  // Analyse les statistiques des actions (en ignorant les groupements avec S)
  analyzeActionStatistics(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): ActionStatistics[] {
    const actionMap = new Map<string, {
      frequency: number;
      withParameters: number;
      withoutParameters: number;
      scenarios: Set<string>;
      parameterTypes: Map<string, number>;
    }>();

    scenarios.forEach(scenario => {
      const fileName = scenario.file || 'unknown';

      scenario.commands.forEach(cmd => {
        // Ignorer les commandes de groupement (se terminant par S)
        if (cmd.action.endsWith('S') && cmd.action !== 'TRANSFERT' && cmd.action !== 'MOTIF') {
          return;
        }

        if (!actionMap.has(cmd.action)) {
          actionMap.set(cmd.action, {
            frequency: 0,
            withParameters: 0,
            withoutParameters: 0,
            scenarios: new Set(),
            parameterTypes: new Map()
          });
        }

        const entry = actionMap.get(cmd.action)!;
        entry.frequency++;
        entry.scenarios.add(fileName);

        if (cmd.parameters) {
          entry.withParameters++;
          entry.parameterTypes.set(cmd.parameters, (entry.parameterTypes.get(cmd.parameters) || 0) + 1);
        } else {
          entry.withoutParameters++;
        }
      });
    });

    return Array.from(actionMap.entries())
      .map(([action, data]) => ({
        action,
        frequency: data.frequency,
        withParameters: data.withParameters,
        withoutParameters: data.withoutParameters,
        scenarios: Array.from(data.scenarios),
        parameterTypes: Object.fromEntries(data.parameterTypes)
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }

  // Génère des étiquettes pour chaque scénario basées sur les actions
  generateScenarioLabels(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): ScenarioLabel[] {
    return scenarios.map(scenario => {
      const fileName = scenario.file || 'unknown';
      const actions = scenario.commands
        .filter(cmd => !cmd.action.endsWith('S') || cmd.action === 'TRANSFERT' || cmd.action === 'MOTIF')
        .map(cmd => cmd.action);

      const usesTransfert = actions.includes('TRANSFERT');
      const usesMotif = actions.includes('MOTIF');
      const usesColor = scenario.commands.some(cmd => cmd.specialSelection?.type === 'COLOR');
      const usesInvert = scenario.commands.some(cmd => cmd.specialSelection?.type === 'INVERT');

      const primaryLabel = this.determinePrimaryLabel(actions, usesTransfert, usesMotif, usesColor);
      const secondaryLabels = this.determineSecondaryLabels(actions, usesColor, usesInvert);
      const confidence = this.calculateLabelConfidence(actions, primaryLabel);

      return {
        scenarioFile: fileName,
        primaryLabel,
        secondaryLabels,
        actionSequence: actions,
        usesTransfert,
        usesMotif,
        usesColor,
        usesInvert,
        complexity: scenario.metadata.complexity,
        confidence
      };
    });
  }

  // Détecte les patterns d'actions récurrents
  detectActionPatterns(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): ActionPattern[] {
    const patternMap = new Map<string, { count: number; scenarios: string[] }>();

    scenarios.forEach(scenario => {
      const fileName = scenario.file || 'unknown';
      const actions = scenario.commands
        .filter(cmd => !cmd.action.endsWith('S') || cmd.action === 'TRANSFERT' || cmd.action === 'MOTIF')
        .map(cmd => cmd.action);

      // Patterns de longueur 2-3 actions
      for (let len = 2; len <= Math.min(3, actions.length); len++) {
        for (let i = 0; i <= actions.length - len; i++) {
          const pattern = actions.slice(i, i + len);
          const key = pattern.join('-');

          if (!patternMap.has(key)) {
            patternMap.set(key, { count: 0, scenarios: [] });
          }

          const entry = patternMap.get(key)!;
          entry.count++;
          if (!entry.scenarios.includes(fileName)) {
            entry.scenarios.push(fileName);
          }
        }
      }
    });

    return Array.from(patternMap.entries())
      .filter(([_, data]) => data.count >= 3)
      .map(([pattern, data]) => ({
        pattern: pattern.split('-'),
        frequency: data.count,
        scenarios: data.scenarios,
        label: this.generatePatternLabel(pattern.split('-'))
      }))
      .sort((a, b) => b.frequency - a.frequency);
  }

  // Analyse l'usage de TRANSFERT, MOTIF et COLOR
  analyzeSpecialUsage(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]) {
    const transfertScenarios: string[] = [];
    const motifScenarios: string[] = [];
    const colorScenarios: string[] = [];

    scenarios.forEach(scenario => {
      const fileName = scenario.file || 'unknown';
      const actions = scenario.commands.map(cmd => cmd.action);
      const hasColor = scenario.commands.some(cmd => cmd.specialSelection?.type === 'COLOR');

      if (actions.includes('TRANSFERT')) {
        transfertScenarios.push(fileName);
      }
      if (actions.includes('MOTIF')) {
        motifScenarios.push(fileName);
      }
      if (hasColor) {
        colorScenarios.push(fileName);
      }
    });

    return {
      transfertUsage: {
        total: transfertScenarios.length,
        percentage: (transfertScenarios.length / scenarios.length) * 100,
        scenarios: transfertScenarios
      },
      motifUsage: {
        total: motifScenarios.length,
        percentage: (motifScenarios.length / scenarios.length) * 100,
        scenarios: motifScenarios
      },
      colorUsage: {
        total: colorScenarios.length,
        percentage: (colorScenarios.length / scenarios.length) * 100,
        scenarios: colorScenarios
      }
    };
  }

  // Analyse complète des actions pour l'étiquetage
  analyzeActions(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata; file?: string }[]): ActionAnalysisResult {
    const actionStatistics = this.analyzeActionStatistics(scenarios);
    const scenarioLabels = this.generateScenarioLabels(scenarios);
    const actionPatterns = this.detectActionPatterns(scenarios);
    const specialUsage = this.analyzeSpecialUsage(scenarios);

    // Distribution des étiquettes
    const labelDistribution: { [label: string]: number } = {};
    scenarioLabels.forEach(label => {
      labelDistribution[label.primaryLabel] = (labelDistribution[label.primaryLabel] || 0) + 1;
    });

    // Insights
    const mostCommonLabel = Object.entries(labelDistribution)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Unknown';

    const mostCommonActionSequence = actionPatterns[0]?.pattern || [];

    const transfertDominance = specialUsage.transfertUsage.percentage > 50;

    const recommendations = this.generateActionRecommendations(
      actionStatistics, specialUsage, labelDistribution
    );

    return {
      actionStatistics,
      scenarioLabels,
      actionPatterns,
      ...specialUsage,
      labelDistribution,
      insights: {
        mostCommonLabel,
        mostCommonActionSequence,
        transfertDominance,
        recommendations
      }
    };
  }

  // Méthodes utilitaires pour l'étiquetage
  private determinePrimaryLabel(actions: string[], usesTransfert: boolean, usesMotif: boolean, usesColor: boolean): string {
    // Priorité 1: TRANSFERT (récupération de test/input)
    if (usesTransfert) {
      return 'TRANSFERT_BASED';
    }

    // Priorité 2: MOTIF avec COLOR (manipulation de motifs colorés)
    if (usesMotif && usesColor) {
      return 'MOTIF_COLOR_MANIPULATION';
    }

    // Priorité 3: MOTIF seul
    if (usesMotif) {
      return 'MOTIF_BASED';
    }

    // Priorité 4: Basé sur les actions principales
    if (actions.includes('FLOODFILL')) {
      return 'FILL_BASED';
    }

    if (actions.includes('COPY') && actions.includes('PASTE')) {
      return 'COPY_PASTE_BASED';
    }

    if (actions.includes('RESIZE')) {
      return 'RESIZE_BASED';
    }

    if (actions.includes('REPLACE')) {
      return 'REPLACE_BASED';
    }

    if (actions.includes('EDIT')) {
      return 'EDIT_BASED';
    }

    return 'UNKNOWN';
  }

  private determineSecondaryLabels(actions: string[], usesColor: boolean, usesInvert: boolean): string[] {
    const labels: string[] = [];

    if (usesColor) {
      labels.push('COLOR_SELECTION');
    }

    if (usesInvert) {
      labels.push('INVERT_SELECTION');
    }

    if (actions.includes('ROTATE')) {
      labels.push('ROTATION');
    }

    if (actions.includes('FLIP')) {
      labels.push('REFLECTION');
    }

    if (actions.length > 5) {
      labels.push('MULTI_STEP');
    }

    return labels;
  }

  private calculateLabelConfidence(actions: string[], primaryLabel: string): number {
    // Confiance basée sur la clarté du pattern d'actions
    let confidence = 0.5; // Base

    switch (primaryLabel) {
      case 'TRANSFERT_BASED':
        confidence = 0.95; // Très haute confiance pour TRANSFERT
        break;
      case 'MOTIF_COLOR_MANIPULATION':
        confidence = 0.9; // Haute confiance pour MOTIF + COLOR
        break;
      case 'MOTIF_BASED':
        confidence = 0.85;
        break;
      case 'FILL_BASED':
      case 'COPY_PASTE_BASED':
      case 'RESIZE_BASED':
        confidence = 0.8;
        break;
      case 'REPLACE_BASED':
      case 'EDIT_BASED':
        confidence = 0.7;
        break;
      default:
        confidence = 0.3;
    }

    // Ajustement basé sur la longueur de la séquence
    if (actions.length === 1) {
      confidence *= 0.8;
    } else if (actions.length > 3) {
      confidence *= 0.9;
    }

    return Math.min(confidence, 1.0);
  }

  private generatePatternLabel(pattern: string[]): string {
    if (pattern.includes('TRANSFERT')) {
      return 'Transfer Pattern';
    }
    if (pattern.includes('MOTIF')) {
      return 'Motif Pattern';
    }
    if (pattern.includes('FLOODFILL')) {
      return 'Fill Pattern';
    }
    if (pattern.includes('COPY') && pattern.includes('PASTE')) {
      return 'Copy-Paste Pattern';
    }

    return `${pattern.join('-')} Pattern`;
  }

  private generateActionRecommendations(
    actionStats: ActionStatistics[],
    specialUsage: any,
    labelDistribution: { [label: string]: number }
  ): string[] {
    const recommendations: string[] = [];

    // Recommandations basées sur TRANSFERT
    if (specialUsage.transfertUsage.percentage > 30) {
      recommendations.push(`TRANSFERT est utilisé dans ${specialUsage.transfertUsage.percentage.toFixed(1)}% des scénarios - Focus sur la récupération test/input`);
    }

    // Recommandations basées sur MOTIF
    if (specialUsage.motifUsage.percentage > 20) {
      recommendations.push(`MOTIF apparaît dans ${specialUsage.motifUsage.percentage.toFixed(1)}% des scénarios - Important pour la manipulation de patterns`);
    }

    // Recommandations basées sur COLOR
    if (specialUsage.colorUsage.percentage > 25) {
      recommendations.push(`COLOR est utilisé dans ${specialUsage.colorUsage.percentage.toFixed(1)}% des scénarios - Crucial pour la sélection par couleur`);
    }

    // Action la plus fréquente
    if (actionStats.length > 0) {
      const topAction = actionStats[0];
      recommendations.push(`Action la plus fréquente: ${topAction.action} (${topAction.frequency} occurrences dans ${topAction.scenarios.length} scénarios)`);
    }

    // Distribution des étiquettes
    const topLabel = Object.entries(labelDistribution)
      .sort(([,a], [,b]) => b - a)[0];
    if (topLabel) {
      recommendations.push(`Type de scénario dominant: ${topLabel[0]} (${topLabel[1]} scénarios)`);
    }

    return recommendations;
  }
}
