import fs from 'fs';
import path from 'path';
import { parseScenarioAGIFile, ScenarioCommand, Selection, SpecialSelection } from './utils/parseScenarioAGI';

const AGI_DIRECTORY = path.join(__dirname, '../arcdata/training');
const OUTPUT_FILE = path.join(__dirname, '../output/knowledge-graph.json');

function readAGIFiles(directory: string): { file: string; content: string }[] {
    return fs.readdirSync(directory)
        .filter(file => file.endsWith('.agi'))
        .map(file => ({ file, content: fs.readFileSync(path.join(directory, file), 'utf-8') }));
}

interface GraphNode {
    id: string;
    type: string;
    label: string;
    file?: string;
}

interface GraphEdge {
    from: string;
    to: string;
    relation: string;
}

interface KnowledgeGraph {
    nodes: GraphNode[];
    edges: GraphEdge[];
}

function scenarioCommandToGraph(cmd: ScenarioCommand, file: string, nodes: GraphNode[], edges: GraphEdge[], parentId?: string) {
    const nodeId = parentId || `${file}-${nodes.length}`;
    nodes.push({ id: nodeId, type: 'action', label: cmd.action, file });
    if (cmd.parameters) {
        const paramId = `${nodeId}-param`;
        nodes.push({ id: paramId, type: 'parameter', label: cmd.parameters, file });
        edges.push({ from: nodeId, to: paramId, relation: 'has_parameter' });
    }
    if (cmd.selections) {
        cmd.selections.forEach((sel, i) => {
            const selId = `${nodeId}-sel-${i}`;
            nodes.push({ id: selId, type: 'selection', label: JSON.stringify(sel), file });
            edges.push({ from: nodeId, to: selId, relation: 'has_selection' });
        });
    }
    if (cmd.specialSelection) {
        const specId = `${nodeId}-special`;
        nodes.push({ id: specId, type: 'special', label: cmd.specialSelection.type, file });
        edges.push({ from: nodeId, to: specId, relation: 'has_special_selection' });
        if ('colors' in cmd.specialSelection && cmd.specialSelection.colors) {
            const colorId = `${specId}-colors`;
            nodes.push({ id: colorId, type: 'color', label: cmd.specialSelection.colors.join(','), file });
            edges.push({ from: specId, to: colorId, relation: 'has_color' });
        }
        cmd.specialSelection.selections.forEach((sel, i) => {
            const selId = `${specId}-sel-${i}`;
            nodes.push({ id: selId, type: 'selection', label: JSON.stringify(sel), file });
            edges.push({ from: specId, to: selId, relation: 'has_selection' });
        });
    }
    if (cmd.group) {
        cmd.group.forEach((subCmd, i) => {
            scenarioCommandToGraph(subCmd, file, nodes, edges, `${nodeId}-group-${i}`);
            edges.push({ from: nodeId, to: `${nodeId}-group-${i}`, relation: 'has_group_command' });
        });
    }
}

function generateKnowledgeGraph() {
    const files = readAGIFiles(AGI_DIRECTORY);
    const nodes: GraphNode[] = [];
    const edges: GraphEdge[] = [];
    files.forEach(({ file, content }) => {
        const commands = parseScenarioAGIFile(content);
        commands.forEach(cmd => scenarioCommandToGraph(cmd, file, nodes, edges));
    });
    const knowledgeGraph: KnowledgeGraph = { nodes, edges };
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(knowledgeGraph, null, 2));
    console.log('Knowledge graph generated successfully and saved to knowledge-graph.json');
}

generateKnowledgeGraph();