TRANSFERT {INIT 17x19; EDIT 2 [0,0]; EDIT 7 [0,1]; EDIT 2 [0,2]; EDIT 2 [0,4]; EDIT 6 [0,5]; EDIT 3 [0,6]; EDIT 3 [0,8]; EDIT 9 [0,9]; EDIT 1 [0,10]; EDIT 3 [0,11]; EDIT 5 [0,12]; EDIT 3 [0,13]; EDIT 4 [0,15]; EDIT 5 [0,16]; EDIT 4 [1,0]; EDIT 4 [1,1]; EDIT 8 [1,2]; EDIT 7 [1,3]; EDIT 7 [1,5]; EDIT 9 [1,6]; EDIT 1 [1,7]; EDIT 4 [1,8]; EDIT 9 [1,9]; EDIT 5 [1,10]; EDIT 2 [1,11]; EDIT 8 [1,13]; EDIT 5 [1,14]; EDIT 3 [1,15]; EDIT 2 [1,16]; EDIT 8 [2,0]; EDIT 7 [2,1]; EDIT 9 [2,2]; EDIT 8 [2,3]; EDIT 8 [2,4]; EDIT 8 [2,5]; EDIT 8 [2,6]; EDIT 8 [2,7]; EDIT 8 [2,8]; EDIT 8 [2,9]; EDIT 8 [2,10]; EDIT 7 [2,11]; EDIT 6 [2,12]; EDIT 1 [2,13]; EDIT 5 [2,14]; EDIT 2 [2,15]; EDIT 1 [2,16]; EDIT 6 [3,0]; EDIT 9 [3,1]; EDIT 3 [3,2]; EDIT 8 [3,3]; EDIT 8 [3,4]; EDIT 8 [3,5]; EDIT 8 [3,6]; EDIT 8 [3,7]; EDIT 8 [3,8]; EDIT 8 [3,9]; EDIT 8 [3,10]; EDIT 7 [3,11]; EDIT 7 [3,12]; EDIT 8 [3,13]; EDIT 1 [3,14]; EDIT 3 [3,15]; EDIT 6 [3,16]; EDIT 2 [4,1]; EDIT 9 [4,2]; EDIT 8 [4,3]; EDIT 8 [4,4]; EDIT 8 [4,5]; EDIT 8 [4,6]; EDIT 8 [4,7]; EDIT 8 [4,8]; EDIT 1 [4,9]; EDIT 8 [4,10]; EDIT 9 [4,11]; EDIT 5 [4,12]; EDIT 1 [4,13]; EDIT 9 [4,14]; EDIT 4 [4,15]; EDIT 1 [4,16]; EDIT 5 [5,0]; EDIT 2 [5,1]; EDIT 6 [5,2]; EDIT 8 [5,3]; EDIT 8 [5,4]; EDIT 8 [5,5]; EDIT 8 [5,6]; EDIT 8 [5,7]; EDIT 8 [5,8]; EDIT 8 [5,9]; EDIT 8 [5,10]; EDIT 8 [5,11]; EDIT 3 [5,12]; EDIT 6 [5,13]; EDIT 7 [5,14]; EDIT 9 [5,15]; EDIT 5 [5,16]; EDIT 8 [6,0]; EDIT 4 [6,1]; EDIT 4 [6,2]; EDIT 8 [6,3]; EDIT 8 [6,4]; EDIT 8 [6,5]; EDIT 8 [6,6]; EDIT 8 [6,7]; EDIT 8 [6,8]; EDIT 8 [6,9]; EDIT 8 [6,10]; EDIT 8 [6,11]; EDIT 7 [6,12]; EDIT 1 [6,13]; EDIT 7 [6,14]; EDIT 3 [6,15]; EDIT 7 [6,16]; EDIT 8 [7,0]; EDIT 6 [7,1]; EDIT 2 [7,2]; EDIT 8 [7,3]; EDIT 8 [7,4]; EDIT 1 [7,5]; EDIT 8 [7,6]; EDIT 8 [7,7]; EDIT 8 [7,8]; EDIT 8 [7,9]; EDIT 8 [7,10]; EDIT 6 [7,11]; EDIT 3 [7,12]; EDIT 1 [7,13]; EDIT 1 [7,14]; EDIT 2 [7,15]; EDIT 9 [7,16]; EDIT 9 [8,0]; EDIT 4 [8,1]; EDIT 8 [8,3]; EDIT 8 [8,4]; EDIT 8 [8,5]; EDIT 8 [8,6]; EDIT 8 [8,7]; EDIT 8 [8,8]; EDIT 8 [8,9]; EDIT 8 [8,10]; EDIT 6 [8,11]; EDIT 4 [8,12]; EDIT 6 [8,14]; EDIT 7 [8,15]; EDIT 6 [8,16]; EDIT 6 [9,0]; EDIT 7 [9,1]; EDIT 7 [9,2]; EDIT 8 [9,3]; EDIT 8 [9,4]; EDIT 8 [9,5]; EDIT 8 [9,6]; EDIT 8 [9,7]; EDIT 8 [9,8]; EDIT 8 [9,9]; EDIT 8 [9,10]; EDIT 4 [9,11]; EDIT 7 [9,12]; EDIT 1 [9,13]; EDIT 5 [9,14]; EDIT 8 [9,15]; EDIT 4 [9,16]; EDIT 4 [10,0]; EDIT 3 [10,2]; EDIT 8 [10,3]; EDIT 8 [10,4]; EDIT 8 [10,5]; EDIT 8 [10,6]; EDIT 8 [10,7]; EDIT 8 [10,8]; EDIT 8 [10,9]; EDIT 8 [10,10]; EDIT 4 [10,11]; EDIT 2 [10,12]; EDIT 4 [10,13]; EDIT 3 [10,14]; EDIT 4 [10,15]; EDIT 5 [10,16]; EDIT 3 [11,0]; EDIT 7 [11,1]; EDIT 7 [11,2]; EDIT 8 [11,3]; EDIT 8 [11,4]; EDIT 8 [11,5]; EDIT 8 [11,6]; EDIT 8 [11,7]; EDIT 8 [11,8]; EDIT 8 [11,9]; EDIT 8 [11,10]; EDIT 4 [11,11]; EDIT 8 [11,12]; EDIT 7 [11,13]; EDIT 7 [11,14]; EDIT 1 [11,15]; EDIT 8 [11,16]; EDIT 6 [12,0]; EDIT 6 [12,1]; EDIT 4 [12,2]; EDIT 7 [12,3]; EDIT 6 [12,4]; EDIT 8 [12,5]; EDIT 1 [12,6]; EDIT 8 [12,7]; EDIT 1 [12,8]; EDIT 9 [12,9]; EDIT 2 [12,10]; EDIT 6 [12,11]; EDIT 8 [12,12]; EDIT 7 [12,13]; EDIT 2 [12,14]; EDIT 8 [12,15]; EDIT 8 [12,16]; EDIT 7 [13,0]; EDIT 3 [13,1]; EDIT 5 [13,2]; EDIT 1 [13,3]; EDIT 4 [13,4]; EDIT 1 [13,5]; EDIT 6 [13,6]; EDIT 4 [13,7]; EDIT 9 [13,8]; EDIT 6 [13,9]; EDIT 7 [13,10]; EDIT 7 [13,11]; EDIT 9 [13,12]; EDIT 2 [13,13]; EDIT 3 [13,14]; EDIT 2 [13,16]; EDIT 9 [14,0]; EDIT 2 [14,1]; EDIT 2 [14,2]; EDIT 5 [14,3]; EDIT 4 [14,4]; EDIT 8 [14,5]; EDIT 3 [14,6]; EDIT 9 [14,7]; EDIT 9 [14,8]; EDIT 9 [14,9]; EDIT 5 [14,10]; EDIT 9 [14,11]; EDIT 6 [14,12]; EDIT 1 [14,13]; EDIT 4 [14,14]; EDIT 6 [14,15]; EDIT 9 [14,16]; EDIT 6 [15,0]; EDIT 1 [15,1]; EDIT 9 [15,2]; EDIT 6 [15,3]; EDIT 3 [15,4]; EDIT 1 [15,5]; EDIT 6 [15,6]; EDIT 6 [15,7]; EDIT 8 [15,8]; EDIT 6 [15,9]; EDIT 1 [15,11]; EDIT 3 [15,12]; EDIT 4 [15,13]; EDIT 8 [15,14]; EDIT 7 [15,15]; EDIT 7 [15,16]; EDIT 2 [16,0]; EDIT 1 [16,1]; EDIT 2 [16,2]; EDIT 4 [16,3]; EDIT 9 [16,4]; EDIT 2 [16,5]; EDIT 1 [16,6]; EDIT 5 [16,7]; EDIT 1 [16,8]; EDIT 7 [16,9]; EDIT 7 [16,11]; EDIT 9 [16,12]; EDIT 3 [16,13]; EDIT 8 [16,14]; EDIT 2 [16,15]; EDIT 1 [16,16]; EDIT 7 [17,0]; EDIT 1 [17,1]; EDIT 9 [17,2]; EDIT 4 [17,3]; EDIT 2 [17,4]; EDIT 8 [17,5]; EDIT 4 [17,6]; EDIT 3 [17,7]; EDIT 6 [17,8]; EDIT 2 [17,9]; EDIT 8 [17,10]; EDIT 8 [17,12]; EDIT 5 [17,13]; EDIT 3 [17,14]; EDIT 5 [17,15]; EDIT 9 [17,16]; EDIT 1 [18,0]; EDIT 2 [18,1]; EDIT 5 [18,2]; EDIT 7 [18,3]; EDIT 8 [18,4]; EDIT 7 [18,5]; EDIT 1 [18,6]; EDIT 6 [18,7]; EDIT 5 [18,8]; EDIT 8 [18,9]; EDIT 9 [18,11]; EDIT 2 [18,12]; EDIT 8 [18,13]; EDIT 9 [18,14]; EDIT 1 [18,15]; EDIT 5 [18,16]}
EXTRACT [2,3 11,10]
FILL 1 ([2,0 2,7] [5,0 5,7] [0,6 9,6] [0,2 9,2])
END