TRANSFERT {INIT 24x24; EDIT 4 [0,0]; EDIT 4 [0,1]; EDIT 4 [0,2]; EDIT 4 [0,4]; EDIT 3 [0,7]; EDIT 3 [0,8]; EDIT 3 [0,9]; EDIT 3 [0,14]; EDIT 3 [0,15]; EDIT 3 [0,16]; EDIT 4 [0,19]; EDIT 4 [0,21]; EDIT 4 [0,22]; EDIT 4 [0,23]; EDIT 4 [1,0]; EDIT 4 [1,1]; EDIT 4 [1,2]; EDIT 4 [1,3]; EDIT 4 [1,5]; EDIT 3 [1,6]; EDIT 3 [1,7]; EDIT 3 [1,8]; EDIT 3 [1,9]; EDIT 3 [1,11]; EDIT 3 [1,12]; EDIT 3 [1,14]; EDIT 3 [1,15]; EDIT 3 [1,16]; EDIT 3 [1,17]; EDIT 4 [1,18]; EDIT 4 [1,20]; EDIT 4 [1,21]; EDIT 4 [1,22]; EDIT 4 [1,23]; EDIT 4 [2,0]; EDIT 4 [2,1]; EDIT 4 [2,3]; EDIT 3 [2,6]; EDIT 3 [2,7]; EDIT 3 [2,10]; EDIT 3 [2,11]; EDIT 3 [2,12]; EDIT 3 [2,13]; EDIT 3 [2,16]; EDIT 3 [2,17]; EDIT 4 [2,20]; EDIT 4 [2,22]; EDIT 4 [2,23]; EDIT 4 [3,1]; EDIT 4 [3,2]; EDIT 4 [3,4]; EDIT 4 [3,5]; EDIT 3 [3,6]; EDIT 3 [3,7]; EDIT 3 [3,10]; EDIT 3 [3,11]; EDIT 3 [3,12]; EDIT 3 [3,13]; EDIT 3 [3,16]; EDIT 3 [3,17]; EDIT 4 [3,18]; EDIT 4 [3,19]; EDIT 4 [3,21]; EDIT 4 [3,22]; EDIT 4 [4,0]; EDIT 4 [4,3]; EDIT 4 [4,4]; EDIT 4 [4,5]; EDIT 3 [4,8]; EDIT 3 [4,9]; EDIT 3 [4,11]; EDIT 3 [4,12]; EDIT 3 [4,14]; EDIT 3 [4,15]; EDIT 4 [4,18]; EDIT 4 [4,19]; EDIT 4 [4,20]; EDIT 4 [4,23]; EDIT 4 [5,1]; EDIT 4 [5,3]; EDIT 4 [5,4]; EDIT 3 [5,7]; EDIT 3 [5,8]; EDIT 3 [5,9]; EDIT 3 [5,10]; EDIT 3 [5,11]; EDIT 3 [5,12]; EDIT 3 [5,13]; EDIT 3 [5,14]; EDIT 3 [5,15]; EDIT 3 [5,16]; EDIT 4 [5,19]; EDIT 4 [5,20]; EDIT 4 [5,22]; EDIT 3 [6,1]; EDIT 3 [6,2]; EDIT 3 [6,3]; EDIT 8 [6,6]; EDIT 8 [6,7]; EDIT 8 [6,8]; EDIT 1 [6,9]; EDIT 1 [6,10]; EDIT 1 [6,11]; EDIT 1 [6,12]; EDIT 1 [6,13]; EDIT 8 [6,14]; EDIT 8 [6,15]; EDIT 8 [6,16]; EDIT 8 [6,17]; EDIT 3 [6,20]; EDIT 3 [6,21]; EDIT 3 [6,22]; EDIT 3 [7,0]; EDIT 3 [7,1]; EDIT 3 [7,2]; EDIT 3 [7,3]; EDIT 3 [7,5]; EDIT 8 [7,6]; EDIT 8 [7,7]; EDIT 8 [7,8]; EDIT 1 [7,9]; EDIT 1 [7,10]; EDIT 1 [7,11]; EDIT 1 [7,12]; EDIT 1 [7,13]; EDIT 8 [7,15]; EDIT 8 [7,16]; EDIT 8 [7,17]; EDIT 3 [7,18]; EDIT 3 [7,20]; EDIT 3 [7,21]; EDIT 3 [7,22]; EDIT 3 [7,23]; EDIT 3 [8,0]; EDIT 3 [8,1]; EDIT 3 [8,4]; EDIT 3 [8,5]; EDIT 8 [8,6]; EDIT 8 [8,7]; EDIT 8 [8,8]; EDIT 1 [8,9]; EDIT 1 [8,10]; EDIT 1 [8,11]; EDIT 1 [8,12]; EDIT 1 [8,13]; EDIT 8 [8,15]; EDIT 8 [8,16]; EDIT 8 [8,17]; EDIT 3 [8,18]; EDIT 3 [8,19]; EDIT 3 [8,22]; EDIT 3 [8,23]; EDIT 3 [9,0]; EDIT 3 [9,1]; EDIT 3 [9,4]; EDIT 3 [9,5]; EDIT 8 [9,6]; EDIT 1 [9,9]; EDIT 1 [9,10]; EDIT 1 [9,11]; EDIT 1 [9,12]; EDIT 1 [9,13]; EDIT 8 [9,14]; EDIT 8 [9,17]; EDIT 3 [9,18]; EDIT 3 [9,19]; EDIT 3 [9,22]; EDIT 3 [9,23]; EDIT 3 [10,2]; EDIT 3 [10,3]; EDIT 3 [10,5]; EDIT 8 [10,6]; EDIT 8 [10,8]; EDIT 1 [10,9]; EDIT 1 [10,10]; EDIT 1 [10,11]; EDIT 1 [10,12]; EDIT 1 [10,13]; EDIT 8 [10,14]; EDIT 8 [10,15]; EDIT 8 [10,17]; EDIT 3 [10,18]; EDIT 3 [10,20]; EDIT 3 [10,21]; EDIT 3 [11,1]; EDIT 3 [11,2]; EDIT 3 [11,3]; EDIT 3 [11,4]; EDIT 3 [11,5]; EDIT 8 [11,6]; EDIT 8 [11,7]; EDIT 8 [11,9]; EDIT 8 [11,10]; EDIT 8 [11,11]; EDIT 8 [11,12]; EDIT 8 [11,13]; EDIT 8 [11,14]; EDIT 8 [11,16]; EDIT 8 [11,17]; EDIT 3 [11,18]; EDIT 3 [11,19]; EDIT 3 [11,20]; EDIT 3 [11,21]; EDIT 3 [11,22]; EDIT 3 [12,1]; EDIT 3 [12,2]; EDIT 3 [12,3]; EDIT 3 [12,4]; EDIT 3 [12,5]; EDIT 8 [12,6]; EDIT 8 [12,7]; EDIT 8 [12,9]; EDIT 8 [12,10]; EDIT 8 [12,11]; EDIT 8 [12,12]; EDIT 8 [12,13]; EDIT 8 [12,14]; EDIT 8 [12,16]; EDIT 8 [12,17]; EDIT 3 [12,18]; EDIT 3 [12,19]; EDIT 3 [12,20]; EDIT 3 [12,21]; EDIT 3 [12,22]; EDIT 3 [13,2]; EDIT 3 [13,3]; EDIT 3 [13,5]; EDIT 8 [13,6]; EDIT 8 [13,8]; EDIT 8 [13,9]; EDIT 8 [13,11]; EDIT 8 [13,12]; EDIT 8 [13,14]; EDIT 8 [13,15]; EDIT 8 [13,17]; EDIT 3 [13,18]; EDIT 3 [13,20]; EDIT 3 [13,21]; EDIT 3 [14,0]; EDIT 3 [14,1]; EDIT 3 [14,4]; EDIT 3 [14,5]; EDIT 8 [14,6]; EDIT 8 [14,9]; EDIT 8 [14,10]; EDIT 8 [14,11]; EDIT 8 [14,12]; EDIT 8 [14,13]; EDIT 8 [14,14]; EDIT 8 [14,17]; EDIT 3 [14,18]; EDIT 3 [14,19]; EDIT 3 [14,22]; EDIT 3 [14,23]; EDIT 3 [15,0]; EDIT 3 [15,1]; EDIT 3 [15,4]; EDIT 3 [15,5]; EDIT 8 [15,6]; EDIT 8 [15,7]; EDIT 8 [15,8]; EDIT 8 [15,10]; EDIT 8 [15,13]; EDIT 8 [15,15]; EDIT 8 [15,16]; EDIT 8 [15,17]; EDIT 3 [15,18]; EDIT 3 [15,19]; EDIT 3 [15,22]; EDIT 3 [15,23]; EDIT 3 [16,0]; EDIT 3 [16,1]; EDIT 3 [16,2]; EDIT 3 [16,3]; EDIT 3 [16,5]; EDIT 8 [16,6]; EDIT 8 [16,7]; EDIT 8 [16,8]; EDIT 8 [16,11]; EDIT 8 [16,12]; EDIT 8 [16,15]; EDIT 8 [16,16]; EDIT 8 [16,17]; EDIT 3 [16,18]; EDIT 3 [16,20]; EDIT 3 [16,21]; EDIT 3 [16,22]; EDIT 3 [16,23]; EDIT 3 [17,1]; EDIT 3 [17,2]; EDIT 3 [17,3]; EDIT 8 [17,6]; EDIT 8 [17,7]; EDIT 8 [17,8]; EDIT 8 [17,9]; EDIT 8 [17,10]; EDIT 8 [17,11]; EDIT 8 [17,12]; EDIT 8 [17,13]; EDIT 8 [17,14]; EDIT 8 [17,15]; EDIT 8 [17,16]; EDIT 8 [17,17]; EDIT 3 [17,20]; EDIT 3 [17,21]; EDIT 3 [17,22]; EDIT 4 [18,1]; EDIT 4 [18,3]; EDIT 4 [18,4]; EDIT 3 [18,7]; EDIT 3 [18,8]; EDIT 3 [18,9]; EDIT 3 [18,10]; EDIT 3 [18,11]; EDIT 3 [18,12]; EDIT 3 [18,13]; EDIT 3 [18,14]; EDIT 3 [18,15]; EDIT 3 [18,16]; EDIT 4 [18,19]; EDIT 4 [18,20]; EDIT 4 [18,22]; EDIT 4 [19,0]; EDIT 4 [19,3]; EDIT 4 [19,4]; EDIT 4 [19,5]; EDIT 3 [19,8]; EDIT 3 [19,9]; EDIT 3 [19,11]; EDIT 3 [19,12]; EDIT 3 [19,14]; EDIT 3 [19,15]; EDIT 4 [19,18]; EDIT 4 [19,19]; EDIT 4 [19,20]; EDIT 4 [19,23]; EDIT 4 [20,1]; EDIT 4 [20,2]; EDIT 4 [20,4]; EDIT 4 [20,5]; EDIT 3 [20,6]; EDIT 3 [20,7]; EDIT 3 [20,10]; EDIT 3 [20,11]; EDIT 3 [20,12]; EDIT 3 [20,13]; EDIT 3 [20,16]; EDIT 3 [20,17]; EDIT 4 [20,18]; EDIT 4 [20,19]; EDIT 4 [20,21]; EDIT 4 [20,22]; EDIT 4 [21,0]; EDIT 4 [21,1]; EDIT 4 [21,3]; EDIT 3 [21,6]; EDIT 3 [21,7]; EDIT 3 [21,10]; EDIT 3 [21,11]; EDIT 3 [21,12]; EDIT 3 [21,13]; EDIT 3 [21,16]; EDIT 3 [21,17]; EDIT 4 [21,20]; EDIT 4 [21,22]; EDIT 4 [21,23]; EDIT 4 [22,0]; EDIT 4 [22,1]; EDIT 4 [22,2]; EDIT 4 [22,3]; EDIT 4 [22,5]; EDIT 3 [22,6]; EDIT 3 [22,7]; EDIT 3 [22,8]; EDIT 3 [22,9]; EDIT 3 [22,11]; EDIT 3 [22,12]; EDIT 3 [22,14]; EDIT 3 [22,15]; EDIT 3 [22,16]; EDIT 3 [22,17]; EDIT 4 [22,18]; EDIT 4 [22,20]; EDIT 4 [22,21]; EDIT 4 [22,22]; EDIT 4 [22,23]; EDIT 4 [23,0]; EDIT 4 [23,1]; EDIT 4 [23,2]; EDIT 4 [23,4]; EDIT 3 [23,7]; EDIT 3 [23,8]; EDIT 3 [23,9]; EDIT 3 [23,14]; EDIT 3 [23,15]; EDIT 3 [23,16]; EDIT 4 [23,19]; EDIT 4 [23,21]; EDIT 4 [23,22]; EDIT 4 [23,23]}
COPY [13,9 17,13]
FLIP VERTICAL [6,9 10,13]
PASTE [6,9]
EXTRACT [6,9 10,13]
END