TRANSFERT {INIT 20x20; EDIT 1 [1,1]; EDIT 1 [1,2]; EDIT 1 [1,3]; EDIT 1 [1,4]; EDIT 1 [1,5]; EDIT 1 [1,9]; EDIT 1 [1,10]; EDIT 1 [1,11]; EDIT 1 [1,12]; EDIT 1 [1,13]; EDIT 1 [1,14]; EDIT 1 [1,15]; EDIT 1 [1,16]; EDIT 1 [2,1]; EDIT 1 [2,2]; EDIT 1 [2,3]; EDIT 1 [2,4]; EDIT 1 [2,5]; EDIT 1 [2,9]; EDIT 1 [2,10]; EDIT 1 [2,11]; EDIT 1 [2,12]; EDIT 1 [2,13]; EDIT 2 [2,14]; EDIT 1 [2,15]; EDIT 1 [2,16]; EDIT 1 [3,1]; EDIT 2 [3,2]; EDIT 1 [3,3]; EDIT 1 [3,4]; EDIT 1 [3,5]; EDIT 1 [3,9]; EDIT 2 [3,10]; EDIT 1 [3,11]; EDIT 1 [3,12]; EDIT 1 [3,13]; EDIT 1 [3,14]; EDIT 1 [3,15]; EDIT 1 [3,16]; EDIT 1 [4,1]; EDIT 1 [4,2]; EDIT 1 [4,3]; EDIT 2 [4,4]; EDIT 1 [4,5]; EDIT 1 [5,1]; EDIT 1 [5,2]; EDIT 1 [5,3]; EDIT 1 [5,4]; EDIT 1 [5,5]; EDIT 1 [6,1]; EDIT 1 [6,2]; EDIT 1 [6,3]; EDIT 1 [6,4]; EDIT 1 [6,5]; EDIT 1 [6,8]; EDIT 1 [6,9]; EDIT 1 [6,10]; EDIT 1 [6,11]; EDIT 2 [6,12]; EDIT 1 [6,13]; EDIT 1 [6,14]; EDIT 2 [6,15]; EDIT 1 [6,16]; EDIT 1 [6,17]; EDIT 1 [7,1]; EDIT 1 [7,2]; EDIT 1 [7,3]; EDIT 1 [7,4]; EDIT 1 [7,5]; EDIT 1 [7,8]; EDIT 1 [7,9]; EDIT 1 [7,10]; EDIT 1 [7,11]; EDIT 1 [7,12]; EDIT 1 [7,13]; EDIT 1 [7,14]; EDIT 1 [7,15]; EDIT 1 [7,16]; EDIT 1 [7,17]; EDIT 1 [8,1]; EDIT 1 [8,2]; EDIT 2 [8,3]; EDIT 1 [8,4]; EDIT 1 [8,5]; EDIT 1 [8,8]; EDIT 1 [8,9]; EDIT 2 [8,10]; EDIT 1 [8,11]; EDIT 1 [8,12]; EDIT 1 [8,13]; EDIT 1 [8,14]; EDIT 1 [8,15]; EDIT 1 [8,16]; EDIT 1 [8,17]; EDIT 1 [9,1]; EDIT 1 [9,2]; EDIT 1 [9,3]; EDIT 1 [9,4]; EDIT 1 [9,5]; EDIT 1 [9,8]; EDIT 1 [9,9]; EDIT 1 [9,10]; EDIT 1 [9,11]; EDIT 1 [9,12]; EDIT 2 [9,13]; EDIT 1 [9,14]; EDIT 1 [9,15]; EDIT 2 [9,16]; EDIT 1 [9,17]; EDIT 1 [10,1]; EDIT 1 [10,2]; EDIT 1 [10,3]; EDIT 1 [10,4]; EDIT 1 [10,5]; EDIT 1 [10,8]; EDIT 1 [10,9]; EDIT 1 [10,10]; EDIT 1 [10,11]; EDIT 1 [10,12]; EDIT 1 [10,13]; EDIT 1 [10,14]; EDIT 1 [10,15]; EDIT 1 [10,16]; EDIT 1 [10,17]; EDIT 1 [11,1]; EDIT 2 [11,2]; EDIT 1 [11,3]; EDIT 1 [11,4]; EDIT 1 [11,5]; EDIT 1 [11,8]; EDIT 2 [11,9]; EDIT 1 [11,10]; EDIT 2 [11,11]; EDIT 1 [11,12]; EDIT 1 [11,13]; EDIT 1 [11,14]; EDIT 1 [11,15]; EDIT 1 [11,16]; EDIT 1 [11,17]; EDIT 1 [12,1]; EDIT 1 [12,2]; EDIT 1 [12,3]; EDIT 1 [12,4]; EDIT 1 [12,5]; EDIT 1 [12,8]; EDIT 1 [12,9]; EDIT 1 [12,10]; EDIT 1 [12,11]; EDIT 1 [12,12]; EDIT 1 [12,13]; EDIT 1 [12,14]; EDIT 2 [12,15]; EDIT 1 [12,16]; EDIT 1 [12,17]; EDIT 1 [13,1]; EDIT 1 [13,2]; EDIT 1 [13,3]; EDIT 2 [13,4]; EDIT 1 [13,5]; EDIT 1 [13,8]; EDIT 1 [13,9]; EDIT 2 [13,10]; EDIT 1 [13,11]; EDIT 2 [13,12]; EDIT 1 [13,13]; EDIT 1 [13,14]; EDIT 1 [13,15]; EDIT 1 [13,16]; EDIT 1 [13,17]; EDIT 1 [14,1]; EDIT 1 [14,2]; EDIT 1 [14,3]; EDIT 1 [14,4]; EDIT 1 [14,5]; EDIT 1 [14,8]; EDIT 1 [14,9]; EDIT 1 [14,10]; EDIT 1 [14,11]; EDIT 1 [14,12]; EDIT 1 [14,13]; EDIT 1 [14,14]; EDIT 1 [14,15]; EDIT 1 [14,16]; EDIT 1 [14,17]; EDIT 1 [15,1]; EDIT 1 [15,2]; EDIT 1 [15,3]; EDIT 1 [15,4]; EDIT 1 [15,5]; EDIT 1 [16,1]; EDIT 1 [16,2]; EDIT 1 [16,3]; EDIT 1 [16,4]; EDIT 1 [16,5]; EDIT 1 [17,1]; EDIT 2 [17,2]; EDIT 1 [17,3]; EDIT 1 [17,4]; EDIT 1 [17,5]; EDIT 1 [17,11]; EDIT 1 [17,12]; EDIT 1 [17,13]; EDIT 1 [17,14]; EDIT 1 [17,15]; EDIT 1 [17,16]; EDIT 1 [17,17]; EDIT 1 [17,18]; EDIT 1 [17,19]; EDIT 1 [18,1]; EDIT 1 [18,2]; EDIT 1 [18,3]; EDIT 1 [18,4]; EDIT 1 [18,5]; EDIT 1 [18,11]; EDIT 1 [18,12]; EDIT 2 [18,13]; EDIT 1 [18,14]; EDIT 1 [18,15]; EDIT 1 [18,16]; EDIT 2 [18,17]; EDIT 1 [18,18]; EDIT 1 [18,19]; EDIT 1 [19,11]; EDIT 1 [19,12]; EDIT 1 [19,13]; EDIT 1 [19,14]; EDIT 2 [19,15]; EDIT 1 [19,16]; EDIT 1 [19,17]; EDIT 2 [19,18]; EDIT 1 [19,19]}
EXTRACT [6,8 14,17]
END