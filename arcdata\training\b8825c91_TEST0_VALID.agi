TRANSFERT {INIT 16x16; EDIT 7 [0,0]; EDIT 7 [0,1]; EDIT 8 [0,2]; EDIT 1 [0,3]; EDIT 9 [0,4]; EDIT 8 [0,5]; EDIT 2 [0,6]; EDIT 6 [0,7]; EDIT 6 [0,8]; EDIT 2 [0,9]; EDIT 8 [0,10]; EDIT 9 [0,11]; EDIT 1 [0,12]; EDIT 8 [0,13]; EDIT 7 [0,14]; EDIT 7 [0,15]; EDIT 7 [1,0]; EDIT 1 [1,1]; EDIT 1 [1,2]; EDIT 8 [1,3]; EDIT 8 [1,4]; EDIT 8 [1,5]; EDIT 6 [1,6]; EDIT 6 [1,7]; EDIT 6 [1,8]; EDIT 6 [1,9]; EDIT 8 [1,10]; EDIT 8 [1,11]; EDIT 8 [1,12]; EDIT 1 [1,13]; EDIT 1 [1,14]; EDIT 7 [1,15]; EDIT 8 [2,0]; EDIT 1 [2,1]; EDIT 6 [2,2]; EDIT 9 [2,3]; EDIT 2 [2,4]; EDIT 6 [2,5]; EDIT 4 [2,6]; EDIT 4 [2,7]; EDIT 4 [2,8]; EDIT 6 [2,9]; EDIT 6 [2,10]; EDIT 2 [2,11]; EDIT 9 [2,12]; EDIT 6 [2,13]; EDIT 1 [2,14]; EDIT 8 [2,15]; EDIT 1 [3,0]; EDIT 8 [3,1]; EDIT 9 [3,2]; EDIT 1 [3,3]; EDIT 6 [3,4]; EDIT 6 [3,5]; EDIT 4 [3,6]; EDIT 4 [3,7]; EDIT 4 [3,8]; EDIT 1 [3,9]; EDIT 6 [3,10]; EDIT 6 [3,11]; EDIT 1 [3,12]; EDIT 9 [3,13]; EDIT 8 [3,14]; EDIT 1 [3,15]; EDIT 9 [4,0]; EDIT 8 [4,1]; EDIT 2 [4,2]; EDIT 6 [4,3]; EDIT 8 [4,4]; EDIT 7 [4,5]; EDIT 4 [4,6]; EDIT 4 [4,7]; EDIT 4 [4,8]; EDIT 6 [4,9]; EDIT 4 [4,10]; EDIT 4 [4,11]; EDIT 4 [4,12]; EDIT 4 [4,13]; EDIT 8 [4,14]; EDIT 9 [4,15]; EDIT 8 [5,0]; EDIT 8 [5,1]; EDIT 6 [5,2]; EDIT 6 [5,3]; EDIT 7 [5,4]; EDIT 7 [5,5]; EDIT 6 [5,6]; EDIT 5 [5,7]; EDIT 5 [5,8]; EDIT 6 [5,9]; EDIT 4 [5,10]; EDIT 4 [5,11]; EDIT 4 [5,12]; EDIT 4 [5,13]; EDIT 8 [5,14]; EDIT 8 [5,15]; EDIT 2 [6,0]; EDIT 6 [6,1]; EDIT 6 [6,2]; EDIT 1 [6,3]; EDIT 6 [6,4]; EDIT 6 [6,5]; EDIT 5 [6,6]; EDIT 5 [6,7]; EDIT 5 [6,8]; EDIT 5 [6,9]; EDIT 4 [6,10]; EDIT 4 [6,11]; EDIT 4 [6,12]; EDIT 4 [6,13]; EDIT 6 [6,14]; EDIT 2 [6,15]; EDIT 6 [7,0]; EDIT 6 [7,1]; EDIT 1 [7,2]; EDIT 1 [7,3]; EDIT 6 [7,4]; EDIT 5 [7,5]; EDIT 5 [7,6]; EDIT 7 [7,7]; EDIT 7 [7,8]; EDIT 5 [7,9]; EDIT 4 [7,10]; EDIT 4 [7,11]; EDIT 4 [7,12]; EDIT 4 [7,13]; EDIT 6 [7,14]; EDIT 6 [7,15]; EDIT 6 [8,0]; EDIT 6 [8,1]; EDIT 1 [8,2]; EDIT 1 [8,3]; EDIT 6 [8,4]; EDIT 5 [8,5]; EDIT 5 [8,6]; EDIT 7 [8,7]; EDIT 7 [8,8]; EDIT 5 [8,9]; EDIT 5 [8,10]; EDIT 6 [8,11]; EDIT 1 [8,12]; EDIT 1 [8,13]; EDIT 6 [8,14]; EDIT 6 [8,15]; EDIT 2 [9,0]; EDIT 6 [9,1]; EDIT 6 [9,2]; EDIT 1 [9,3]; EDIT 6 [9,4]; EDIT 6 [9,5]; EDIT 5 [9,6]; EDIT 5 [9,7]; EDIT 5 [9,8]; EDIT 5 [9,9]; EDIT 6 [9,10]; EDIT 6 [9,11]; EDIT 1 [9,12]; EDIT 6 [9,13]; EDIT 6 [9,14]; EDIT 2 [9,15]; EDIT 8 [10,0]; EDIT 8 [10,1]; EDIT 6 [10,2]; EDIT 6 [10,3]; EDIT 7 [10,4]; EDIT 7 [10,5]; EDIT 6 [10,6]; EDIT 5 [10,7]; EDIT 5 [10,8]; EDIT 6 [10,9]; EDIT 7 [10,10]; EDIT 7 [10,11]; EDIT 6 [10,12]; EDIT 6 [10,13]; EDIT 8 [10,14]; EDIT 8 [10,15]; EDIT 9 [11,0]; EDIT 8 [11,1]; EDIT 2 [11,2]; EDIT 6 [11,3]; EDIT 8 [11,4]; EDIT 7 [11,5]; EDIT 6 [11,6]; EDIT 6 [11,7]; EDIT 6 [11,8]; EDIT 6 [11,9]; EDIT 7 [11,10]; EDIT 8 [11,11]; EDIT 6 [11,12]; EDIT 2 [11,13]; EDIT 8 [11,14]; EDIT 9 [11,15]; EDIT 1 [12,0]; EDIT 8 [12,1]; EDIT 9 [12,2]; EDIT 1 [12,3]; EDIT 6 [12,4]; EDIT 6 [12,5]; EDIT 1 [12,6]; EDIT 1 [12,7]; EDIT 1 [12,8]; EDIT 1 [12,9]; EDIT 6 [12,10]; EDIT 6 [12,11]; EDIT 1 [12,12]; EDIT 9 [12,13]; EDIT 8 [12,14]; EDIT 1 [12,15]; EDIT 8 [13,0]; EDIT 1 [13,1]; EDIT 6 [13,2]; EDIT 9 [13,3]; EDIT 2 [13,4]; EDIT 6 [13,5]; EDIT 6 [13,6]; EDIT 1 [13,7]; EDIT 1 [13,8]; EDIT 6 [13,9]; EDIT 6 [13,10]; EDIT 2 [13,11]; EDIT 9 [13,12]; EDIT 6 [13,13]; EDIT 1 [13,14]; EDIT 8 [13,15]; EDIT 7 [14,0]; EDIT 1 [14,1]; EDIT 1 [14,2]; EDIT 8 [14,3]; EDIT 8 [14,4]; EDIT 8 [14,5]; EDIT 6 [14,6]; EDIT 6 [14,7]; EDIT 6 [14,8]; EDIT 6 [14,9]; EDIT 8 [14,10]; EDIT 8 [14,11]; EDIT 8 [14,12]; EDIT 1 [14,13]; EDIT 1 [14,14]; EDIT 7 [14,15]; EDIT 7 [15,0]; EDIT 7 [15,1]; EDIT 8 [15,2]; EDIT 1 [15,3]; EDIT 9 [15,4]; EDIT 8 [15,5]; EDIT 2 [15,6]; EDIT 6 [15,7]; EDIT 6 [15,8]; EDIT 2 [15,9]; EDIT 8 [15,10]; EDIT 9 [15,11]; EDIT 1 [15,12]; EDIT 8 [15,13]; EDIT 7 [15,14]; EDIT 7 [15,15]}
COPY [11,6 13,8]
FLIP VERTICAL [11,6 13,8]
PASTE [2,6]
COPY [4,2 7,5]
FLIP HORIZONTAL [4,2 7,5]
PASTE [4,10]
END