TRANSFERT {INIT 18x19; EDIT 1 [3,1]; EDIT 1 [3,2]; EDIT 1 [3,3]; EDIT 1 [3,4]; EDIT 1 [3,5]; EDIT 1 [3,6]; EDIT 1 [3,7]; EDIT 1 [3,8]; EDIT 1 [3,9]; EDIT 1 [3,10]; EDIT 1 [3,11]; EDIT 1 [3,12]; EDIT 1 [3,13]; EDIT 1 [3,14]; EDIT 1 [4,1]; EDIT 1 [4,2]; EDIT 1 [4,3]; EDIT 1 [4,4]; EDIT 1 [4,5]; EDIT 8 [4,6]; EDIT 1 [4,7]; EDIT 1 [4,8]; EDIT 1 [4,9]; EDIT 8 [4,10]; EDIT 1 [4,11]; EDIT 1 [4,12]; EDIT 1 [4,13]; EDIT 1 [4,14]; EDIT 1 [5,1]; EDIT 1 [5,2]; EDIT 8 [5,3]; EDIT 8 [5,4]; EDIT 8 [5,5]; EDIT 8 [5,6]; EDIT 8 [5,7]; EDIT 8 [5,8]; EDIT 8 [5,9]; EDIT 8 [5,10]; EDIT 8 [5,11]; EDIT 8 [5,12]; EDIT 1 [5,13]; EDIT 1 [5,14]; EDIT 1 [6,1]; EDIT 1 [6,2]; EDIT 8 [6,3]; EDIT 8 [6,4]; EDIT 8 [6,5]; EDIT 8 [6,6]; EDIT 8 [6,7]; EDIT 8 [6,8]; EDIT 8 [6,9]; EDIT 8 [6,10]; EDIT 8 [6,11]; EDIT 8 [6,12]; EDIT 1 [6,13]; EDIT 1 [6,14]; EDIT 1 [7,1]; EDIT 1 [7,2]; EDIT 8 [7,3]; EDIT 8 [7,4]; EDIT 8 [7,5]; EDIT 8 [7,6]; EDIT 8 [7,7]; EDIT 8 [7,8]; EDIT 8 [7,9]; EDIT 8 [7,10]; EDIT 8 [7,11]; EDIT 8 [7,12]; EDIT 1 [7,13]; EDIT 1 [7,14]; EDIT 1 [8,1]; EDIT 1 [8,2]; EDIT 8 [8,3]; EDIT 8 [8,4]; EDIT 8 [8,5]; EDIT 8 [8,6]; EDIT 8 [8,7]; EDIT 8 [8,8]; EDIT 8 [8,9]; EDIT 8 [8,10]; EDIT 8 [8,11]; EDIT 8 [8,12]; EDIT 1 [8,13]; EDIT 1 [8,14]; EDIT 1 [9,1]; EDIT 8 [9,2]; EDIT 8 [9,3]; EDIT 8 [9,4]; EDIT 8 [9,5]; EDIT 8 [9,6]; EDIT 8 [9,7]; EDIT 8 [9,8]; EDIT 8 [9,9]; EDIT 8 [9,10]; EDIT 8 [9,11]; EDIT 8 [9,12]; EDIT 1 [9,13]; EDIT 1 [9,14]; EDIT 1 [10,1]; EDIT 1 [10,2]; EDIT 8 [10,3]; EDIT 8 [10,4]; EDIT 8 [10,5]; EDIT 8 [10,6]; EDIT 8 [10,7]; EDIT 8 [10,8]; EDIT 8 [10,9]; EDIT 8 [10,10]; EDIT 8 [10,11]; EDIT 8 [10,12]; EDIT 1 [10,13]; EDIT 1 [10,14]; EDIT 1 [11,1]; EDIT 1 [11,2]; EDIT 8 [11,3]; EDIT 8 [11,4]; EDIT 8 [11,5]; EDIT 8 [11,6]; EDIT 8 [11,7]; EDIT 8 [11,8]; EDIT 8 [11,9]; EDIT 8 [11,10]; EDIT 8 [11,11]; EDIT 8 [11,12]; EDIT 1 [11,13]; EDIT 1 [11,14]; EDIT 1 [12,1]; EDIT 1 [12,2]; EDIT 8 [12,3]; EDIT 8 [12,4]; EDIT 8 [12,5]; EDIT 8 [12,6]; EDIT 8 [12,7]; EDIT 8 [12,8]; EDIT 8 [12,9]; EDIT 8 [12,10]; EDIT 8 [12,11]; EDIT 8 [12,12]; EDIT 1 [12,13]; EDIT 1 [12,14]; EDIT 1 [13,1]; EDIT 1 [13,2]; EDIT 8 [13,3]; EDIT 8 [13,4]; EDIT 8 [13,5]; EDIT 8 [13,6]; EDIT 8 [13,7]; EDIT 8 [13,8]; EDIT 8 [13,9]; EDIT 8 [13,10]; EDIT 8 [13,11]; EDIT 8 [13,12]; EDIT 1 [13,13]; EDIT 1 [13,14]; EDIT 1 [14,1]; EDIT 1 [14,2]; EDIT 1 [14,3]; EDIT 1 [14,4]; EDIT 1 [14,5]; EDIT 1 [14,6]; EDIT 1 [14,7]; EDIT 1 [14,8]; EDIT 1 [14,9]; EDIT 1 [14,10]; EDIT 1 [14,11]; EDIT 1 [14,12]; EDIT 1 [14,13]; EDIT 1 [14,14]; EDIT 1 [15,1]; EDIT 1 [15,2]; EDIT 1 [15,3]; EDIT 1 [15,4]; EDIT 1 [15,5]; EDIT 1 [15,6]; EDIT 1 [15,7]; EDIT 1 [15,8]; EDIT 1 [15,9]; EDIT 1 [15,10]; EDIT 1 [15,11]; EDIT 1 [15,12]; EDIT 1 [15,13]; EDIT 1 [15,14]}
FILLS {FILL 1 ([9,3 9,12] [5,6 13,6] [5,10 13,10]); FILL 8 ([0,6 2,6] [0,10 2,10] [9,15 9,17] [9,0] [9,0] [16,6 18,6] [16,10 18,10])}
EDIT 1 ([9,2] [4,6] [4,10])
END