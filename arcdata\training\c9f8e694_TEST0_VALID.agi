TRANSFERT {INIT 12x12; EDIT 1 [0,0]; EDIT 5 [0,2]; EDIT 5 [0,3]; EDIT 5 [0,4]; EDIT 5 [0,5]; EDIT 5 [0,6]; EDIT 5 [0,7]; EDIT 8 [1,0]; EDIT 5 [1,2]; EDIT 5 [1,3]; EDIT 5 [1,4]; EDIT 5 [1,5]; EDIT 5 [1,6]; EDIT 5 [1,7]; EDIT 1 [2,0]; EDIT 5 [2,2]; EDIT 5 [2,3]; EDIT 5 [2,4]; EDIT 5 [2,5]; EDIT 5 [2,6]; EDIT 5 [2,7]; EDIT 1 [3,0]; EDIT 5 [3,2]; EDIT 5 [3,3]; EDIT 5 [3,4]; EDIT 5 [3,5]; EDIT 5 [3,6]; EDIT 5 [3,7]; EDIT 5 [3,9]; EDIT 5 [3,10]; EDIT 5 [3,11]; EDIT 7 [4,0]; EDIT 5 [4,2]; EDIT 5 [4,3]; EDIT 5 [4,4]; EDIT 5 [4,5]; EDIT 5 [4,6]; EDIT 5 [4,7]; EDIT 5 [4,9]; EDIT 5 [4,10]; EDIT 5 [4,11]; EDIT 7 [5,0]; EDIT 5 [5,2]; EDIT 5 [5,3]; EDIT 5 [5,4]; EDIT 5 [5,5]; EDIT 5 [5,6]; EDIT 5 [5,7]; EDIT 5 [5,9]; EDIT 5 [5,10]; EDIT 5 [5,11]; EDIT 7 [6,0]; EDIT 5 [6,6]; EDIT 5 [6,7]; EDIT 5 [6,8]; EDIT 5 [6,9]; EDIT 5 [6,10]; EDIT 5 [6,11]; EDIT 7 [7,0]; EDIT 5 [7,6]; EDIT 5 [7,7]; EDIT 5 [7,8]; EDIT 8 [8,0]; EDIT 5 [8,6]; EDIT 5 [8,7]; EDIT 5 [8,8]; EDIT 8 [9,0]; EDIT 5 [9,2]; EDIT 5 [9,3]; EDIT 5 [9,4]; EDIT 5 [9,6]; EDIT 5 [9,7]; EDIT 5 [9,8]; EDIT 8 [10,0]; EDIT 5 [10,2]; EDIT 5 [10,3]; EDIT 5 [10,4]; EDIT 5 [10,6]; EDIT 5 [10,7]; EDIT 5 [10,8]; EDIT 8 [11,0]; EDIT 5 [11,2]; EDIT 5 [11,3]; EDIT 5 [11,4]; EDIT 5 [11,6]; EDIT 5 [11,7]; EDIT 5 [11,8]}
FLOODFILL 1 [0,2]
EDITS {EDIT 8 [1,2]; EDIT 8 [1,3]; EDIT 8 [1,4]; EDIT 8 [1,5]; EDIT 8 [1,6]; EDIT 8 [1,7]; EDIT 7 [4,2]; EDIT 7 [4,3]; EDIT 7 [4,4]; EDIT 7 [4,5]; EDIT 7 [4,6]; EDIT 7 [4,7]; EDIT 7 [5,7]; EDIT 7 [5,6]; EDIT 7 [5,5]; EDIT 7 [5,4]; EDIT 7 [5,3]; EDIT 7 [5,2]; EDIT 7 [4,9]; EDIT 7 [4,10]; EDIT 7 [4,11]; EDIT 7 [5,11]; EDIT 7 [6,11]; EDIT 7 [6,10]; EDIT 7 [6,9]; EDIT 7 [5,9]; EDIT 7 [5,10]; EDIT 7 [6,6]; EDIT 7 [6,7]; EDIT 7 [6,8]; EDIT 7 [7,8]; EDIT 7 [7,7]; EDIT 7 [7,6]}
FLOODFILLS {FLOODFILL 8 [10,3]; FLOODFILL 8 [10,8]}
END