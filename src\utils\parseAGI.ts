import fs from 'fs';
import path from 'path';

export interface AGIData {
    id: string;
    label: string;
    related?: string[];
}

export function parseAGIFiles(directory: string): AGIData[] {
    const agiFiles = fs.readdirSync(directory).filter(file => file.endsWith('.agi'));
    const knowledgeGraph: AGIData[] = [];

    agiFiles.forEach(file => {
        const filePath = path.join(directory, file);
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        const parsedData = extractRelevantInformation(fileContent, file);
        knowledgeGraph.push(parsedData);
    });

    return knowledgeGraph;
}

function extractRelevantInformation(content: string, fileName: string): AGIData {
    // Example: parse id from filename, label from first line, related from lines starting with 'related:'
    const lines = content.split('\n');
    const id = fileName.replace('.agi', '');
    const label = lines[0] || id;
    const related: string[] = lines.filter(l => l.startsWith('related:')).map(l => l.replace('related:', '').trim());
    return { id, label, related };
}