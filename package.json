{"name": "arc-knowledge-graph", "version": "1.0.0", "description": "A project to generate a knowledge graph from AGI files.", "main": "src/generateGraph.ts", "scripts": {"build": "tsc", "start": "node dist/generateGraph.js", "generate": "npm run build && node dist/generateGraph.js"}, "dependencies": {"fs": "^0.0.1-security", "path": "^0.12.7"}, "devDependencies": {"typescript": "^4.5.4", "@types/node": "^16.11.7"}, "repository": {"type": "git", "url": "https://github.com/yourusername/arc-knowledge-graph.git"}, "keywords": ["knowledge-graph", "AGI", "data-processing"], "author": "Your Name", "license": "MIT"}