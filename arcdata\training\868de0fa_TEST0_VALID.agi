TRANSFERT {INIT 20x20; EDIT 1 [0,12]; EDIT 1 [0,13]; EDIT 1 [0,14]; EDIT 1 [0,15]; EDIT 1 [0,16]; EDIT 1 [1,12]; EDIT 1 [1,16]; EDIT 1 [2,1]; EDIT 1 [2,2]; EDIT 1 [2,3]; EDIT 1 [2,4]; EDIT 1 [2,5]; EDIT 1 [2,6]; EDIT 1 [2,7]; EDIT 1 [2,8]; EDIT 1 [2,12]; EDIT 1 [2,16]; EDIT 1 [3,1]; EDIT 1 [3,8]; EDIT 1 [3,12]; EDIT 1 [3,16]; EDIT 1 [4,1]; EDIT 1 [4,8]; EDIT 1 [4,12]; EDIT 1 [4,13]; EDIT 1 [4,14]; EDIT 1 [4,15]; EDIT 1 [4,16]; EDIT 1 [5,1]; EDIT 1 [5,8]; EDIT 1 [6,1]; EDIT 1 [6,8]; EDIT 1 [7,1]; EDIT 1 [7,8]; EDIT 1 [7,10]; EDIT 1 [7,11]; EDIT 1 [7,12]; EDIT 1 [7,13]; EDIT 1 [7,14]; EDIT 1 [7,15]; EDIT 1 [7,16]; EDIT 1 [7,17]; EDIT 1 [7,18]; EDIT 1 [7,19]; EDIT 1 [8,1]; EDIT 1 [8,8]; EDIT 1 [8,10]; EDIT 1 [8,19]; EDIT 1 [9,1]; EDIT 1 [9,2]; EDIT 1 [9,3]; EDIT 1 [9,4]; EDIT 1 [9,5]; EDIT 1 [9,6]; EDIT 1 [9,7]; EDIT 1 [9,8]; EDIT 1 [9,10]; EDIT 1 [9,19]; EDIT 1 [10,10]; EDIT 1 [10,19]; EDIT 1 [11,1]; EDIT 1 [11,2]; EDIT 1 [11,3]; EDIT 1 [11,4]; EDIT 1 [11,5]; EDIT 1 [11,6]; EDIT 1 [11,7]; EDIT 1 [11,10]; EDIT 1 [11,19]; EDIT 1 [12,1]; EDIT 1 [12,7]; EDIT 1 [12,10]; EDIT 1 [12,19]; EDIT 1 [13,1]; EDIT 1 [13,7]; EDIT 1 [13,10]; EDIT 1 [13,19]; EDIT 1 [14,1]; EDIT 1 [14,7]; EDIT 1 [14,10]; EDIT 1 [14,19]; EDIT 1 [15,1]; EDIT 1 [15,7]; EDIT 1 [15,10]; EDIT 1 [15,19]; EDIT 1 [16,1]; EDIT 1 [16,7]; EDIT 1 [16,10]; EDIT 1 [16,11]; EDIT 1 [16,12]; EDIT 1 [16,13]; EDIT 1 [16,14]; EDIT 1 [16,15]; EDIT 1 [16,16]; EDIT 1 [16,17]; EDIT 1 [16,18]; EDIT 1 [16,19]; EDIT 1 [17,1]; EDIT 1 [17,2]; EDIT 1 [17,3]; EDIT 1 [17,4]; EDIT 1 [17,5]; EDIT 1 [17,6]; EDIT 1 [17,7]}
FLOODFILLS {FLOODFILL 2 [5,4]; FLOODFILL 2 [10,15]; FLOODFILL 7 [2,14]; FLOODFILL 7 [14,3]}
END