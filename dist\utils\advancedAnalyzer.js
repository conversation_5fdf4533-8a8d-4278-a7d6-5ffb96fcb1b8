"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedScenarioAnalyzer = void 0;
class AdvancedScenarioAnalyzer {
    // Analyse les dimensions et propriétés des grilles
    analyzeGrids(scenarios) {
        const dimensions = [];
        const resizeOperations = [];
        scenarios.forEach(scenario => {
            scenario.metadata.gridSizes.forEach(size => {
                dimensions.push(size);
            });
            // Détection des opérations de redimensionnement
            scenario.commands.forEach(cmd => {
                var _a;
                if (cmd.action === 'RESIZE' && ((_a = cmd.metadata) === null || _a === void 0 ? void 0 : _a.gridSize)) {
                    const newSize = cmd.metadata.gridSize;
                    const prevSize = dimensions[dimensions.length - 1];
                    if (prevSize) {
                        const factor = (newSize.width * newSize.height) / (prevSize.width * prevSize.height);
                        resizeOperations.push({
                            from: `${prevSize.width}x${prevSize.height}`,
                            to: `${newSize.width}x${newSize.height}`,
                            factor
                        });
                    }
                }
            });
        });
        const aspectRatios = dimensions.map(d => d.width / d.height);
        const areas = dimensions.map(d => d.width * d.height);
        const sizeCategories = {
            small: areas.filter(a => a <= 25).length,
            medium: areas.filter(a => a > 25 && a <= 100).length,
            large: areas.filter(a => a > 100).length
        };
        return {
            dimensions,
            aspectRatios,
            sizeCategories,
            resizeOperations
        };
    }
    // Analyse avancée des couleurs et transitions
    analyzeColors(scenarios) {
        const colorFrequency = {};
        const colorTransitions = {};
        let monochromaticScenarios = 0;
        scenarios.forEach(scenario => {
            const scenarioColors = new Set(scenario.metadata.colorsUsed);
            if (scenarioColors.size <= 2) {
                monochromaticScenarios++;
            }
            scenario.metadata.colorsUsed.forEach(color => {
                colorFrequency[color] = (colorFrequency[color] || 0) + 1;
            });
            // Analyse des transitions de couleurs dans les commandes REPLACE
            scenario.commands.forEach(cmd => {
                var _a;
                if (cmd.action === 'REPLACE' && ((_a = cmd.metadata) === null || _a === void 0 ? void 0 : _a.colors) && cmd.metadata.colors.length >= 2) {
                    const from = cmd.metadata.colors[0];
                    const to = cmd.metadata.colors[1];
                    const key = `${from}->${to}`;
                    colorTransitions[key] = (colorTransitions[key] || 0) + 1;
                }
            });
        });
        const palette = Object.keys(colorFrequency).map(Number).sort((a, b) => a - b);
        const dominantColors = Object.entries(colorFrequency)
            .map(([color, freq]) => ({ color: Number(color), frequency: freq }))
            .sort((a, b) => b.frequency - a.frequency)
            .slice(0, 10);
        const colorTransitionsList = Object.entries(colorTransitions)
            .map(([transition, freq]) => {
            const [from, to] = transition.split('->').map(Number);
            return { from, to, frequency: freq };
        })
            .sort((a, b) => b.frequency - a.frequency);
        const colorComplexity = palette.length / scenarios.length;
        return {
            palette,
            dominantColors,
            colorTransitions: colorTransitionsList,
            colorComplexity,
            monochromaticScenarios
        };
    }
    // Analyse des patterns géométriques et de transformation
    analyzePatterns(scenarios) {
        const geometricPatterns = {
            rectangles: 0,
            lines: 0,
            points: 0,
            complexShapes: 0
        };
        const transformationPatterns = {
            translations: 0,
            rotations: 0,
            reflections: 0,
            scalings: 0
        };
        const sequencePatterns = {
            repetitive: 0,
            progressive: 0,
            alternating: 0
        };
        scenarios.forEach(scenario => {
            // Analyse des formes géométriques basée sur les sélections
            scenario.commands.forEach(cmd => {
                if (cmd.selections) {
                    cmd.selections.forEach(sel => {
                        if (this.isCoordRect(sel)) {
                            const rect = sel;
                            const width = Math.abs(rect.to.col - rect.from.col) + 1;
                            const height = Math.abs(rect.to.row - rect.from.row) + 1;
                            if (width === 1 && height === 1) {
                                geometricPatterns.points++;
                            }
                            else if (width === 1 || height === 1) {
                                geometricPatterns.lines++;
                            }
                            else {
                                geometricPatterns.rectangles++;
                            }
                        }
                        else {
                            geometricPatterns.points++;
                        }
                    });
                }
                // Détection des transformations
                if (cmd.action.includes('ROTATE')) {
                    transformationPatterns.rotations++;
                }
                else if (cmd.action.includes('FLIP')) {
                    transformationPatterns.reflections++;
                }
                else if (cmd.action === 'RESIZE') {
                    transformationPatterns.scalings++;
                }
                else if (cmd.action === 'COPY' || cmd.action === 'PASTE') {
                    transformationPatterns.translations++;
                }
            });
            // Analyse des séquences de commandes
            const actions = scenario.commands.map(cmd => cmd.action);
            if (this.hasRepetitivePattern(actions)) {
                sequencePatterns.repetitive++;
            }
            if (this.hasProgressivePattern(actions)) {
                sequencePatterns.progressive++;
            }
            if (this.hasAlternatingPattern(actions)) {
                sequencePatterns.alternating++;
            }
        });
        return {
            geometricPatterns,
            transformationPatterns,
            sequencePatterns
        };
    }
    // Analyse des stratégies de résolution
    analyzeStrategies(scenarios) {
        const solutionStrategies = {
            directEdit: 0,
            copyPaste: 0,
            floodFill: 0,
            structuralTransform: 0,
            multiStep: 0
        };
        const complexityLevels = {
            trivial: 0,
            simple: 0,
            moderate: 0,
            complex: 0,
            expert: 0
        };
        const allConcepts = new Set();
        let totalMemoryRequired = 0;
        let totalStepsRequired = 0;
        scenarios.forEach(scenario => {
            const actions = scenario.commands.map(cmd => cmd.action);
            const uniqueActions = new Set(actions);
            // Classification des stratégies
            if (uniqueActions.has('EDIT') && actions.length <= 3) {
                solutionStrategies.directEdit++;
            }
            else if (uniqueActions.has('COPY') && uniqueActions.has('PASTE')) {
                solutionStrategies.copyPaste++;
            }
            else if (uniqueActions.has('FLOODFILL')) {
                solutionStrategies.floodFill++;
            }
            else if (uniqueActions.has('RESIZE') || uniqueActions.has('ROTATE') || uniqueActions.has('FLIP')) {
                solutionStrategies.structuralTransform++;
            }
            if (actions.length > 5) {
                solutionStrategies.multiStep++;
            }
            // Classification de la complexité
            const complexity = scenario.metadata.complexity;
            if (complexity < 5) {
                complexityLevels.trivial++;
            }
            else if (complexity < 10) {
                complexityLevels.simple++;
            }
            else if (complexity < 20) {
                complexityLevels.moderate++;
            }
            else if (complexity < 35) {
                complexityLevels.complex++;
            }
            else {
                complexityLevels.expert++;
            }
            // Calcul de la charge cognitive
            totalMemoryRequired += scenario.metadata.colorsUsed.length + scenario.metadata.gridSizes.length;
            totalStepsRequired += actions.length;
            // Collecte des concepts
            uniqueActions.forEach(action => allConcepts.add(action));
            scenario.metadata.transformationTypes.forEach(type => allConcepts.add(type));
        });
        return {
            solutionStrategies,
            complexityLevels,
            cognitiveLoad: {
                memoryRequired: totalMemoryRequired / scenarios.length,
                stepsRequired: totalStepsRequired / scenarios.length,
                conceptsRequired: Array.from(allConcepts)
            }
        };
    }
    // Analyse complète avec corrélations
    analyzeScenarios(scenarios) {
        const gridAnalysis = this.analyzeGrids(scenarios);
        const colorAnalysis = this.analyzeColors(scenarios);
        const patternAnalysis = this.analyzePatterns(scenarios);
        const strategyAnalysis = this.analyzeStrategies(scenarios);
        // Calcul des corrélations
        const correlations = this.calculateCorrelations(scenarios, gridAnalysis, colorAnalysis);
        return {
            gridAnalysis,
            colorAnalysis,
            patternAnalysis,
            strategyAnalysis,
            correlations
        };
    }
    // Méthodes utilitaires privées
    isCoordRect(selection) {
        return 'from' in selection && 'to' in selection;
    }
    hasRepetitivePattern(actions) {
        if (actions.length < 4)
            return false;
        for (let i = 0; i < actions.length - 1; i++) {
            if (actions[i] === actions[i + 1])
                return true;
        }
        return false;
    }
    hasProgressivePattern(actions) {
        const editCommands = actions.filter(a => a === 'EDIT');
        return editCommands.length > 3;
    }
    hasAlternatingPattern(actions) {
        if (actions.length < 4)
            return false;
        for (let i = 0; i < actions.length - 3; i++) {
            if (actions[i] === actions[i + 2] && actions[i] !== actions[i + 1]) {
                return true;
            }
        }
        return false;
    }
    calculateCorrelations(scenarios, gridAnalysis, colorAnalysis) {
        // Corrélation taille de grille vs complexité
        const gridSizes = scenarios.map(s => s.metadata.gridSizes[0] ? s.metadata.gridSizes[0].width * s.metadata.gridSizes[0].height : 0);
        const complexities = scenarios.map(s => s.metadata.complexity);
        const gridSizeVsComplexity = this.pearsonCorrelation(gridSizes, complexities);
        // Corrélation nombre de couleurs vs complexité
        const colorCounts = scenarios.map(s => s.metadata.colorsUsed.length);
        const colorCountVsComplexity = this.pearsonCorrelation(colorCounts, complexities);
        // Patterns vs stratégies
        const patternTypeVsStrategy = {};
        scenarios.forEach(scenario => {
            const patterns = scenario.metadata.patterns;
            const actions = scenario.commands.map(cmd => cmd.action);
            patterns.forEach(pattern => {
                if (!patternTypeVsStrategy[pattern]) {
                    patternTypeVsStrategy[pattern] = [];
                }
                patternTypeVsStrategy[pattern].push(...actions);
            });
        });
        return {
            gridSizeVsComplexity,
            colorCountVsComplexity,
            patternTypeVsStrategy
        };
    }
    pearsonCorrelation(x, y) {
        const n = Math.min(x.length, y.length);
        if (n === 0)
            return 0;
        const sumX = x.slice(0, n).reduce((a, b) => a + b, 0);
        const sumY = y.slice(0, n).reduce((a, b) => a + b, 0);
        const sumXY = x.slice(0, n).reduce((sum, xi, i) => sum + xi * y[i], 0);
        const sumX2 = x.slice(0, n).reduce((sum, xi) => sum + xi * xi, 0);
        const sumY2 = y.slice(0, n).reduce((sum, yi) => sum + yi * yi, 0);
        const numerator = n * sumXY - sumX * sumY;
        const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
        return denominator === 0 ? 0 : numerator / denominator;
    }
}
exports.AdvancedScenarioAnalyzer = AdvancedScenarioAnalyzer;
