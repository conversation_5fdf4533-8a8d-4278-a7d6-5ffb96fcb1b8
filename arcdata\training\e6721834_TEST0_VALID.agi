TRANSFERT {INIT 12x24; EDIT 4 [0,0]; EDIT 4 [0,1]; EDIT 4 [0,2]; EDIT 4 [0,3]; EDIT 4 [0,4]; EDIT 4 [0,5]; EDIT 4 [0,6]; EDIT 4 [0,7]; EDIT 4 [0,8]; EDIT 4 [0,9]; EDIT 4 [0,10]; EDIT 4 [0,11]; EDIT 4 [1,0]; EDIT 4 [1,1]; EDIT 4 [1,2]; EDIT 4 [1,3]; EDIT 4 [1,4]; EDIT 4 [1,5]; EDIT 4 [1,6]; EDIT 4 [1,7]; EDIT 4 [1,8]; EDIT 4 [1,9]; EDIT 4 [1,10]; EDIT 4 [1,11]; EDIT 4 [2,0]; EDIT 4 [2,1]; EDIT 4 [2,2]; EDIT 4 [2,3]; EDIT 4 [2,4]; EDIT 4 [2,5]; EDIT 4 [2,6]; EDIT 4 [2,7]; EDIT 4 [2,8]; EDIT 4 [2,9]; EDIT 4 [2,10]; EDIT 4 [2,11]; EDIT 4 [3,0]; EDIT 4 [3,1]; EDIT 4 [3,2]; EDIT 4 [3,3]; EDIT 4 [3,4]; EDIT 4 [3,5]; EDIT 4 [3,6]; EDIT 1 [3,7]; EDIT 4 [3,8]; EDIT 4 [3,9]; EDIT 4 [3,10]; EDIT 4 [3,11]; EDIT 4 [4,0]; EDIT 4 [4,1]; EDIT 4 [4,2]; EDIT 4 [4,3]; EDIT 4 [4,4]; EDIT 4 [4,5]; EDIT 4 [4,6]; EDIT 4 [4,7]; EDIT 1 [4,8]; EDIT 4 [4,9]; EDIT 4 [4,10]; EDIT 4 [4,11]; EDIT 4 [5,0]; EDIT 4 [5,1]; EDIT 4 [5,2]; EDIT 4 [5,3]; EDIT 4 [5,4]; EDIT 4 [5,5]; EDIT 4 [5,6]; EDIT 4 [5,7]; EDIT 4 [5,8]; EDIT 4 [5,9]; EDIT 4 [5,10]; EDIT 4 [5,11]; EDIT 4 [6,0]; EDIT 4 [6,1]; EDIT 4 [6,2]; EDIT 1 [6,3]; EDIT 4 [6,4]; EDIT 4 [6,5]; EDIT 4 [6,6]; EDIT 4 [6,7]; EDIT 4 [6,8]; EDIT 4 [6,9]; EDIT 4 [6,10]; EDIT 4 [6,11]; EDIT 4 [7,0]; EDIT 1 [7,1]; EDIT 4 [7,2]; EDIT 4 [7,3]; EDIT 4 [7,4]; EDIT 4 [7,5]; EDIT 4 [7,6]; EDIT 4 [7,7]; EDIT 4 [7,8]; EDIT 4 [7,9]; EDIT 4 [7,10]; EDIT 4 [7,11]; EDIT 4 [8,0]; EDIT 4 [8,1]; EDIT 4 [8,2]; EDIT 1 [8,3]; EDIT 4 [8,4]; EDIT 4 [8,5]; EDIT 4 [8,6]; EDIT 4 [8,7]; EDIT 4 [8,8]; EDIT 4 [8,9]; EDIT 4 [8,10]; EDIT 4 [8,11]; EDIT 4 [9,0]; EDIT 4 [9,1]; EDIT 4 [9,2]; EDIT 4 [9,3]; EDIT 4 [9,4]; EDIT 4 [9,5]; EDIT 4 [9,6]; EDIT 4 [9,7]; EDIT 4 [9,8]; EDIT 4 [9,9]; EDIT 4 [9,10]; EDIT 4 [9,11]; EDIT 4 [10,0]; EDIT 4 [10,1]; EDIT 4 [10,2]; EDIT 4 [10,3]; EDIT 4 [10,4]; EDIT 4 [10,5]; EDIT 4 [10,6]; EDIT 4 [10,7]; EDIT 4 [10,8]; EDIT 4 [10,9]; EDIT 4 [10,10]; EDIT 4 [10,11]; EDIT 4 [11,0]; EDIT 4 [11,1]; EDIT 4 [11,2]; EDIT 4 [11,3]; EDIT 4 [11,4]; EDIT 4 [11,5]; EDIT 4 [11,6]; EDIT 4 [11,7]; EDIT 4 [11,8]; EDIT 4 [11,9]; EDIT 4 [11,10]; EDIT 4 [11,11]; EDIT 2 [12,0]; EDIT 2 [12,1]; EDIT 2 [12,2]; EDIT 2 [12,3]; EDIT 2 [12,4]; EDIT 2 [12,5]; EDIT 2 [12,6]; EDIT 2 [12,7]; EDIT 2 [12,8]; EDIT 2 [12,9]; EDIT 2 [12,10]; EDIT 2 [12,11]; EDIT 2 [13,0]; EDIT 2 [13,1]; EDIT 2 [13,2]; EDIT 2 [13,3]; EDIT 2 [13,4]; EDIT 2 [13,5]; EDIT 2 [13,6]; EDIT 2 [13,7]; EDIT 2 [13,8]; EDIT 2 [13,9]; EDIT 2 [13,10]; EDIT 2 [13,11]; EDIT 2 [14,0]; EDIT 2 [14,1]; EDIT 2 [14,2]; EDIT 2 [14,3]; EDIT 2 [14,4]; EDIT 2 [14,5]; EDIT 2 [14,6]; EDIT 2 [14,7]; EDIT 2 [14,8]; EDIT 2 [14,9]; EDIT 2 [14,10]; EDIT 2 [14,11]; EDIT 2 [15,0]; EDIT 2 [15,1]; EDIT 2 [15,2]; EDIT 8 [15,3]; EDIT 8 [15,4]; EDIT 8 [15,5]; EDIT 8 [15,6]; EDIT 8 [15,7]; EDIT 2 [15,8]; EDIT 2 [15,9]; EDIT 2 [15,10]; EDIT 2 [15,11]; EDIT 2 [16,0]; EDIT 2 [16,1]; EDIT 2 [16,2]; EDIT 8 [16,3]; EDIT 8 [16,4]; EDIT 8 [16,5]; EDIT 1 [16,6]; EDIT 8 [16,7]; EDIT 2 [16,8]; EDIT 2 [16,9]; EDIT 2 [16,10]; EDIT 2 [16,11]; EDIT 2 [17,0]; EDIT 2 [17,1]; EDIT 2 [17,2]; EDIT 8 [17,3]; EDIT 8 [17,4]; EDIT 8 [17,5]; EDIT 8 [17,6]; EDIT 1 [17,7]; EDIT 2 [17,8]; EDIT 2 [17,9]; EDIT 2 [17,10]; EDIT 2 [17,11]; EDIT 2 [18,0]; EDIT 2 [18,1]; EDIT 2 [18,2]; EDIT 2 [18,3]; EDIT 2 [18,4]; EDIT 2 [18,5]; EDIT 2 [18,6]; EDIT 2 [18,7]; EDIT 2 [18,8]; EDIT 2 [18,9]; EDIT 2 [18,10]; EDIT 2 [18,11]; EDIT 2 [19,0]; EDIT 8 [19,1]; EDIT 8 [19,2]; EDIT 2 [19,3]; EDIT 2 [19,4]; EDIT 2 [19,5]; EDIT 2 [19,6]; EDIT 2 [19,7]; EDIT 2 [19,8]; EDIT 2 [19,9]; EDIT 2 [19,10]; EDIT 2 [19,11]; EDIT 2 [20,0]; EDIT 1 [20,1]; EDIT 8 [20,2]; EDIT 2 [20,3]; EDIT 2 [20,4]; EDIT 2 [20,5]; EDIT 8 [20,6]; EDIT 8 [20,7]; EDIT 1 [20,8]; EDIT 8 [20,9]; EDIT 8 [20,10]; EDIT 2 [20,11]; EDIT 2 [21,0]; EDIT 8 [21,1]; EDIT 8 [21,2]; EDIT 2 [21,3]; EDIT 2 [21,4]; EDIT 2 [21,5]; EDIT 1 [21,6]; EDIT 8 [21,7]; EDIT 8 [21,8]; EDIT 8 [21,9]; EDIT 8 [21,10]; EDIT 2 [21,11]; EDIT 2 [22,0]; EDIT 8 [22,1]; EDIT 8 [22,2]; EDIT 2 [22,3]; EDIT 2 [22,4]; EDIT 2 [22,5]; EDIT 8 [22,6]; EDIT 8 [22,7]; EDIT 1 [22,8]; EDIT 8 [22,9]; EDIT 8 [22,10]; EDIT 2 [22,11]; EDIT 2 [23,0]; EDIT 2 [23,1]; EDIT 2 [23,2]; EDIT 2 [23,3]; EDIT 2 [23,4]; EDIT 2 [23,5]; EDIT 2 [23,6]; EDIT 2 [23,7]; EDIT 2 [23,8]; EDIT 2 [23,9]; EDIT 2 [23,10]; EDIT 2 [23,11]}
CUT [20,6 22,10]
PASTE [6,1]
CUT [15,3 17,7]
PASTE [2,4]
EXTRACT [0,0 11,11]
END