// Parser avancé pour les fichiers AGI selon la grammaire des scénarios
// Inspiré de FONCTIONNEMENT_COMMANDES_SCENARIOS.md

export type Coord = { row: number; col: number };
export type CoordRect = { from: Coord; to: Coord };
export type Selection = Coord | CoordRect;

export type SpecialSelection =
  | { type: 'INVERT'; selections: Selection[] }
  | { type: 'COLOR'; colors: number[]; selections: Selection[] };

export type ScenarioCommand = {
  action: string;
  parameters?: string;
  selections?: Selection[];
  specialSelection?: SpecialSelection;
  group?: ScenarioCommand[];
};

// Parse une ligne de commande AGI en objet ScenarioCommand
export function parseScenarioCommand(line: string): ScenarioCommand | null {
  // Nettoyage
  const clean = line.trim().replace(/;$/, '');
  if (!clean) return null;

  // Regroupement (accolades)
  if (clean.startsWith('{') && clean.endsWith('}')) {
    const inner = clean.slice(1, -1);
    const commands = inner.split(';').map(cmd => parseScenarioCommand(cmd)).filter(Boolean) as ScenarioCommand[];
    return { action: 'GROUP', group: commands };
  }

  // Extraction action, paramètres, sélections
  const actionMatch = clean.match(/^(\w+)/);
  if (!actionMatch) return null;
  const action = actionMatch[1];

  // Paramètres (avant parenthèses ou crochets)
  const paramMatch = clean.match(/^\w+\s+([^\(\[]+)/);
  const parameters = paramMatch ? paramMatch[1].trim() : undefined;

  // Sélections spéciales (INVERT, COLOR)
  const specialMatch = clean.match(/\((INVERT|COLOR)([^\)]*)\)/);
  if (specialMatch) {
    const type = specialMatch[1];
    const rest = specialMatch[2];
    if (type === 'INVERT') {
      const selections = extractSelections(rest);
      return { action, parameters, specialSelection: { type: 'INVERT', selections } };
    }
    if (type === 'COLOR') {
      const colorParams = rest.match(/\d+(,\d+)*/);
      const colors = colorParams ? colorParams[0].split(',').map(Number) : [];
      const selRest = rest.replace(colorParams ? colorParams[0] : '', '');
      const selections = extractSelections(selRest);
      return { action, parameters, specialSelection: { type: 'COLOR', colors, selections } };
    }
  }

  // Sélections standard (crochets ou parenthèses)
  const selMatch = clean.match(/\(([^\)]*)\)/);
  const selections = selMatch ? extractSelections(selMatch[1]) : extractSelections(clean);

  return { action, parameters, selections };
}

// Extraction des coordonnées
function extractSelections(str: string): Selection[] {
  const results: Selection[] = [];
  const coordRegex = /\[(\d+),(\d+)(?:\s+(\d+),(\d+))?\]/g;
  let match;
  while ((match = coordRegex.exec(str))) {
    if (match[3] && match[4]) {
      results.push({ from: { row: Number(match[1]), col: Number(match[2]) }, to: { row: Number(match[3]), col: Number(match[4]) } });
    } else {
      results.push({ row: Number(match[1]), col: Number(match[2]) });
    }
  }
  return results;
}

// Parse un fichier AGI complet
export function parseScenarioAGIFile(content: string): ScenarioCommand[] {
  return content
    .split(/\r?\n/)
    .map(line => parseScenarioCommand(line))
    .filter(Boolean) as ScenarioCommand[];
}
