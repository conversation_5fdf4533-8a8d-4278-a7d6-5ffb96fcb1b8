// Parser robuste pour les fichiers AGI selon la grammaire des scénarios
// Basé sur FONCTIONNEMENT_COMMANDES_SCENARIOS.md

export type Coord = { row: number; col: number };
export type CoordRect = { from: Coord; to: Coord };
export type Selection = Coord | CoordRect;

export type SpecialSelection =
  | { type: 'INVERT'; selections: Selection[] }
  | { type: 'COLOR'; colors: number[]; selections: Selection[] };

export type ScenarioCommand = {
  action: string;
  parameters?: string;
  selections?: Selection[];
  specialSelection?: SpecialSelection;
  group?: ScenarioCommand[];
  metadata?: {
    gridSize?: { width: number; height: number };
    colors?: number[];
    transformationType?: string;
    complexity?: number;
  };
};

export type ScenarioMetadata = {
  totalCommands: number;
  uniqueActions: string[];
  gridSizes: { width: number; height: number }[];
  colorsUsed: number[];
  transformationTypes: string[];
  complexity: number;
  patterns: string[];
};

// Parse une ligne de commande AGI en objet ScenarioCommand avec gestion robuste
export function parseScenarioCommand(line: string): ScenarioCommand | null {
  const clean = line.trim().replace(/;$/, '');
  if (!clean || clean === 'END') return null;

  // Gestion des commandes groupées avec accolades
  if (clean.includes('{') && clean.includes('}')) {
    return parseGroupedCommand(clean);
  }

  // Extraction de l'action principale
  const actionMatch = clean.match(/^(\w+)/);
  if (!actionMatch) return null;
  const action = actionMatch[1];

  // Extraction des paramètres et sélections selon le type de commande
  return parseCommandByType(action, clean);
}

// Parse les commandes groupées (ex: TRANSFERT {INIT 3x3; EDIT 7 [...]})
function parseGroupedCommand(line: string): ScenarioCommand | null {
  const actionMatch = line.match(/^(\w+)\s*\{([^}]+)\}/);
  if (!actionMatch) return null;

  const action = actionMatch[1];
  const groupContent = actionMatch[2];

  // Parse les commandes internes
  const commands = parseGroupContent(groupContent);

  return {
    action,
    group: commands,
    metadata: extractCommandMetadata(action, line)
  };
}

// Parse le contenu d'un groupe de commandes
function parseGroupContent(content: string): ScenarioCommand[] {
  const commands: ScenarioCommand[] = [];
  const parts = content.split(';').map(p => p.trim()).filter(p => p);

  for (const part of parts) {
    const cmd = parseScenarioCommand(part);
    if (cmd) commands.push(cmd);
  }

  return commands;
}

// Parse une commande selon son type spécifique
function parseCommandByType(action: string, line: string): ScenarioCommand | null {
  switch (action) {
    case 'INIT':
      return parseInitCommand(line);
    case 'EDIT':
      return parseEditCommand(line);
    case 'RESIZE':
      return parseResizeCommand(line);
    case 'COPY':
    case 'PASTE':
      return parseCopyPasteCommand(action, line);
    case 'REPLACE':
      return parseReplaceCommand(line);
    case 'FLOODFILL':
      return parseFloodFillCommand(line);
    default:
      return parseGenericCommand(action, line);
  }
}

// Parsers spécialisés pour chaque type de commande
function parseInitCommand(line: string): ScenarioCommand | null {
  const match = line.match(/INIT\s+(\d+)x(\d+)/);
  if (!match) return null;

  const width = Number(match[1]);
  const height = Number(match[2]);

  return {
    action: 'INIT',
    parameters: `${width}x${height}`,
    metadata: {
      gridSize: { width, height },
      transformationType: 'initialization'
    }
  };
}

function parseEditCommand(line: string): ScenarioCommand | null {
  const match = line.match(/EDIT\s+(\d+)\s+(.+)/);
  if (!match) return null;

  const color = Number(match[1]);
  const selections = extractSelections(match[2]);

  return {
    action: 'EDIT',
    parameters: match[1],
    selections,
    metadata: {
      colors: [color],
      transformationType: 'edit'
    }
  };
}

function parseResizeCommand(line: string): ScenarioCommand | null {
  const match = line.match(/RESIZE\s+(\d+)x(\d+)/);
  if (!match) return null;

  const width = Number(match[1]);
  const height = Number(match[2]);

  return {
    action: 'RESIZE',
    parameters: `${width}x${height}`,
    metadata: {
      gridSize: { width, height },
      transformationType: 'resize'
    }
  };
}

function parseCopyPasteCommand(action: string, line: string): ScenarioCommand | null {
  const selections = extractSelections(line);

  return {
    action,
    selections,
    metadata: {
      transformationType: action.toLowerCase()
    }
  };
}

function parseReplaceCommand(line: string): ScenarioCommand | null {
  const match = line.match(/REPLACE\s+(\d+)\s+(\d+)\s+(.+)/);
  if (!match) return null;

  const fromColor = Number(match[1]);
  const toColor = Number(match[2]);
  const selections = extractSelections(match[3]);

  return {
    action: 'REPLACE',
    parameters: `${fromColor} ${toColor}`,
    selections,
    metadata: {
      colors: [fromColor, toColor],
      transformationType: 'replace'
    }
  };
}

function parseFloodFillCommand(line: string): ScenarioCommand | null {
  const match = line.match(/FLOODFILL\s+(\d+)\s+(.+)/);
  if (!match) return null;

  const color = Number(match[1]);
  const selections = extractSelections(match[2]);

  return {
    action: 'FLOODFILL',
    parameters: match[1],
    selections,
    metadata: {
      colors: [color],
      transformationType: 'floodfill'
    }
  };
}

function parseGenericCommand(action: string, line: string): ScenarioCommand | null {
  const paramMatch = line.match(/^\w+\s+([^\[\(]+)/);
  const parameters = paramMatch ? paramMatch[1].trim() : undefined;

  const selections = extractSelections(line);

  return {
    action,
    parameters,
    selections,
    metadata: {
      transformationType: 'generic'
    }
  };
}

// Extraction des métadonnées d'une commande
function extractCommandMetadata(action: string, line: string): any {
  const metadata: any = { transformationType: action.toLowerCase() };

  // Extraction des couleurs
  const colorMatches = line.match(/\d+/g);
  if (colorMatches) {
    metadata.colors = colorMatches.map(Number);
  }

  // Extraction des tailles de grille
  const gridMatch = line.match(/(\d+)x(\d+)/);
  if (gridMatch) {
    metadata.gridSize = { width: Number(gridMatch[1]), height: Number(gridMatch[2]) };
  }

  return metadata;
}

// Extraction des coordonnées améliorée
function extractSelections(str: string): Selection[] {
  const results: Selection[] = [];
  const coordRegex = /\[(\d+),(\d+)(?:\s+(\d+),(\d+))?\]/g;
  let match;
  while ((match = coordRegex.exec(str))) {
    if (match[3] && match[4]) {
      results.push({ from: { row: Number(match[1]), col: Number(match[2]) }, to: { row: Number(match[3]), col: Number(match[4]) } });
    } else {
      results.push({ row: Number(match[1]), col: Number(match[2]) });
    }
  }
  return results;
}

// Parse un fichier AGI complet avec extraction de métadonnées
export function parseScenarioAGIFile(content: string): { commands: ScenarioCommand[]; metadata: ScenarioMetadata } {
  const lines = content.split(/\r?\n/).filter(line => line.trim() && line.trim() !== 'END');
  const commands = lines
    .map(line => parseScenarioCommand(line))
    .filter(Boolean) as ScenarioCommand[];

  const metadata = extractScenarioMetadata(commands);

  return { commands, metadata };
}

// Extraction des métadonnées globales du scénario
function extractScenarioMetadata(commands: ScenarioCommand[]): ScenarioMetadata {
  const uniqueActions = new Set<string>();
  const gridSizes: { width: number; height: number }[] = [];
  const colorsUsed = new Set<number>();
  const transformationTypes = new Set<string>();
  let totalCommands = 0;

  function processCommand(cmd: ScenarioCommand) {
    totalCommands++;
    uniqueActions.add(cmd.action);

    if (cmd.metadata) {
      if (cmd.metadata.gridSize) {
        gridSizes.push(cmd.metadata.gridSize);
      }
      if (cmd.metadata.colors) {
        cmd.metadata.colors.forEach(color => colorsUsed.add(color));
      }
      if (cmd.metadata.transformationType) {
        transformationTypes.add(cmd.metadata.transformationType);
      }
    }

    if (cmd.group) {
      cmd.group.forEach(processCommand);
    }
  }

  commands.forEach(processCommand);

  // Calcul de la complexité basé sur le nombre de commandes et types d'actions
  const complexity = totalCommands + uniqueActions.size * 2;

  // Détection de patterns basiques
  const patterns: string[] = [];
  if (uniqueActions.has('EDIT') && uniqueActions.has('FLOODFILL')) {
    patterns.push('edit-and-fill');
  }
  if (uniqueActions.has('COPY') && uniqueActions.has('PASTE')) {
    patterns.push('copy-paste');
  }
  if (uniqueActions.has('RESIZE')) {
    patterns.push('grid-transformation');
  }
  if (gridSizes.length > 1) {
    patterns.push('multi-grid');
  }

  return {
    totalCommands,
    uniqueActions: Array.from(uniqueActions),
    gridSizes,
    colorsUsed: Array.from(colorsUsed),
    transformationTypes: Array.from(transformationTypes),
    complexity,
    patterns
  };
}

// Fonction utilitaire pour analyser les patterns dans un ensemble de scénarios
export function analyzeScenarioPatterns(scenarios: { commands: ScenarioCommand[]; metadata: ScenarioMetadata }[]): {
  commonActions: string[];
  commonPatterns: string[];
  complexityDistribution: { low: number; medium: number; high: number };
  colorUsageStats: { [color: number]: number };
} {
  const actionCounts: { [action: string]: number } = {};
  const patternCounts: { [pattern: string]: number } = {};
  const colorCounts: { [color: number]: number } = {};
  let lowComplexity = 0, mediumComplexity = 0, highComplexity = 0;

  scenarios.forEach(scenario => {
    // Comptage des actions
    scenario.metadata.uniqueActions.forEach(action => {
      actionCounts[action] = (actionCounts[action] || 0) + 1;
    });

    // Comptage des patterns
    scenario.metadata.patterns.forEach(pattern => {
      patternCounts[pattern] = (patternCounts[pattern] || 0) + 1;
    });

    // Comptage des couleurs
    scenario.metadata.colorsUsed.forEach(color => {
      colorCounts[color] = (colorCounts[color] || 0) + 1;
    });

    // Distribution de complexité
    if (scenario.metadata.complexity < 10) lowComplexity++;
    else if (scenario.metadata.complexity < 20) mediumComplexity++;
    else highComplexity++;
  });

  // Actions communes (présentes dans plus de 50% des scénarios)
  const threshold = scenarios.length * 0.5;
  const commonActions = Object.entries(actionCounts)
    .filter(([_, count]) => count > threshold)
    .map(([action, _]) => action);

  const commonPatterns = Object.entries(patternCounts)
    .filter(([_, count]) => count > threshold)
    .map(([pattern, _]) => pattern);

  return {
    commonActions,
    commonPatterns,
    complexityDistribution: { low: lowComplexity, medium: mediumComplexity, high: highComplexity },
    colorUsageStats: colorCounts
  };
}
