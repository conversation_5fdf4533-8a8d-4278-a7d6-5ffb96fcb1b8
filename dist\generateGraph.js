"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const parseScenarioAGI_1 = require("./utils/parseScenarioAGI");
const advancedAnalyzer_1 = require("./utils/advancedAnalyzer");
const patternDetector_1 = require("./utils/patternDetector");
const AGI_DIRECTORY = path_1.default.join(__dirname, '../arcdata/training');
const OUTPUT_FILE = path_1.default.join(__dirname, '../output/knowledge-graph.json');
function readAGIFiles(directory) {
    return fs_1.default.readdirSync(directory)
        .filter(file => file.endsWith('.agi'))
        .map(file => ({ file, content: fs_1.default.readFileSync(path_1.default.join(directory, file), 'utf-8') }));
}
function scenarioCommandToGraph(cmd, file, nodes, edges, parentId) {
    const nodeId = parentId || `${file}-${nodes.length}`;
    nodes.push({ id: nodeId, type: 'action', label: cmd.action, file });
    if (cmd.parameters) {
        const paramId = `${nodeId}-param`;
        nodes.push({ id: paramId, type: 'parameter', label: cmd.parameters, file });
        edges.push({ from: nodeId, to: paramId, relation: 'has_parameter' });
    }
    if (cmd.selections) {
        cmd.selections.forEach((sel, i) => {
            const selId = `${nodeId}-sel-${i}`;
            nodes.push({ id: selId, type: 'selection', label: JSON.stringify(sel), file });
            edges.push({ from: nodeId, to: selId, relation: 'has_selection' });
        });
    }
    if (cmd.specialSelection) {
        const specId = `${nodeId}-special`;
        nodes.push({ id: specId, type: 'special', label: cmd.specialSelection.type, file });
        edges.push({ from: nodeId, to: specId, relation: 'has_special_selection' });
        if ('colors' in cmd.specialSelection && cmd.specialSelection.colors) {
            const colorId = `${specId}-colors`;
            nodes.push({ id: colorId, type: 'color', label: cmd.specialSelection.colors.join(','), file });
            edges.push({ from: specId, to: colorId, relation: 'has_color' });
        }
        cmd.specialSelection.selections.forEach((sel, i) => {
            const selId = `${specId}-sel-${i}`;
            nodes.push({ id: selId, type: 'selection', label: JSON.stringify(sel), file });
            edges.push({ from: specId, to: selId, relation: 'has_selection' });
        });
    }
    if (cmd.group) {
        cmd.group.forEach((subCmd, i) => {
            scenarioCommandToGraph(subCmd, file, nodes, edges, `${nodeId}-group-${i}`);
            edges.push({ from: nodeId, to: `${nodeId}-group-${i}`, relation: 'has_group_command' });
        });
    }
}
function generateKnowledgeGraph() {
    const files = readAGIFiles(AGI_DIRECTORY);
    const nodes = [];
    const edges = [];
    const allScenarios = [];
    // Parse tous les fichiers et collecte les métadonnées
    files.forEach(({ file, content }) => {
        const parsed = (0, parseScenarioAGI_1.parseScenarioAGIFile)(content);
        allScenarios.push(parsed);
        // Génère le graphe pour chaque commande
        parsed.commands.forEach((cmd) => scenarioCommandToGraph(cmd, file, nodes, edges));
        // Ajoute un nœud pour les métadonnées du scénario
        const metadataNodeId = `${file}-metadata`;
        nodes.push({
            id: metadataNodeId,
            type: 'scenario_metadata',
            label: `Scenario: ${file}`,
            file
        });
        // Ajoute des nœuds pour les patterns détectés
        parsed.metadata.patterns.forEach((pattern, i) => {
            const patternNodeId = `${file}-pattern-${i}`;
            nodes.push({
                id: patternNodeId,
                type: 'pattern',
                label: pattern,
                file
            });
            edges.push({
                from: metadataNodeId,
                to: patternNodeId,
                relation: 'has_pattern'
            });
        });
    });
    // Analyse globale des patterns
    const globalPatterns = (0, parseScenarioAGI_1.analyzeScenarioPatterns)(allScenarios);
    // Analyse sémantique avancée
    const analyzer = new advancedAnalyzer_1.AdvancedScenarioAnalyzer();
    const semanticInsights = analyzer.analyzeScenarios(allScenarios);
    // Analyse des actions pour l'étiquetage
    const actionAnalyzer = new patternDetector_1.ScenarioActionAnalyzer();
    const actionAnalysis = actionAnalyzer.analyzeActions(allScenarios);
    // Ajoute des nœuds pour les patterns globaux
    globalPatterns.commonActions.forEach(action => {
        const actionNodeId = `global-action-${action}`;
        nodes.push({
            id: actionNodeId,
            type: 'global_action',
            label: `Common Action: ${action}`
        });
    });
    globalPatterns.commonPatterns.forEach(pattern => {
        const patternNodeId = `global-pattern-${pattern}`;
        nodes.push({
            id: patternNodeId,
            type: 'global_pattern',
            label: `Common Pattern: ${pattern}`
        });
    });
    // Ajoute des nœuds pour les insights sémantiques
    addSemanticInsightNodes(nodes, edges, semanticInsights);
    // Ajoute des nœuds pour l'analyse des actions
    addActionAnalysisNodes(nodes, edges, actionAnalysis);
    const knowledgeGraph = {
        nodes,
        edges,
        metadata: {
            totalScenarios: files.length,
            globalPatterns,
            semanticInsights,
            actionAnalysis,
            statistics: {
                totalNodes: nodes.length,
                totalEdges: edges.length,
                nodeTypes: [...new Set(nodes.map(n => n.type))],
                relationTypes: [...new Set(edges.map(e => e.relation))]
            }
        }
    };
    fs_1.default.writeFileSync(OUTPUT_FILE, JSON.stringify(knowledgeGraph, null, 2));
    console.log(`Knowledge graph generated successfully with ${nodes.length} nodes and ${edges.length} edges`);
    console.log(`Analyzed ${files.length} scenarios with ${globalPatterns.commonActions.length} common actions`);
    console.log(`Common patterns found: ${globalPatterns.commonPatterns.join(', ')}`);
    console.log(`Semantic insights: ${semanticInsights.colorAnalysis.dominantColors.length} dominant colors, ${semanticInsights.strategyAnalysis.cognitiveLoad.conceptsRequired.length} concepts`);
    console.log(`Action analysis: TRANSFERT used in ${actionAnalysis.transfertUsage.total} scenarios (${actionAnalysis.transfertUsage.percentage.toFixed(1)}%)`);
    console.log(`Most common label: ${actionAnalysis.insights.mostCommonLabel}`);
}
// Fonction pour ajouter des nœuds basés sur les insights sémantiques
function addSemanticInsightNodes(nodes, edges, insights) {
    // Nœuds pour l'analyse des grilles
    const gridAnalysisNodeId = 'semantic-grid-analysis';
    nodes.push({
        id: gridAnalysisNodeId,
        type: 'semantic_analysis',
        label: `Grid Analysis: ${insights.gridAnalysis.dimensions.length} grids analyzed`
    });
    // Nœuds pour les catégories de taille
    Object.entries(insights.gridAnalysis.sizeCategories).forEach(([category, count]) => {
        const categoryNodeId = `grid-size-${category}`;
        nodes.push({
            id: categoryNodeId,
            type: 'grid_category',
            label: `${category.charAt(0).toUpperCase() + category.slice(1)} Grids: ${count}`
        });
        edges.push({
            from: gridAnalysisNodeId,
            to: categoryNodeId,
            relation: 'has_size_category'
        });
    });
    // Nœuds pour l'analyse des couleurs
    const colorAnalysisNodeId = 'semantic-color-analysis';
    nodes.push({
        id: colorAnalysisNodeId,
        type: 'semantic_analysis',
        label: `Color Analysis: ${insights.colorAnalysis.palette.length} colors in palette`
    });
    // Nœuds pour les couleurs dominantes
    insights.colorAnalysis.dominantColors.slice(0, 5).forEach((colorInfo) => {
        const colorNodeId = `dominant-color-${colorInfo.color}`;
        nodes.push({
            id: colorNodeId,
            type: 'dominant_color',
            label: `Color ${colorInfo.color}: ${colorInfo.frequency} uses`
        });
        edges.push({
            from: colorAnalysisNodeId,
            to: colorNodeId,
            relation: 'has_dominant_color'
        });
    });
    // Nœuds pour l'analyse des patterns
    const patternAnalysisNodeId = 'semantic-pattern-analysis';
    nodes.push({
        id: patternAnalysisNodeId,
        type: 'semantic_analysis',
        label: 'Pattern Analysis'
    });
    // Nœuds pour les patterns géométriques
    Object.entries(insights.patternAnalysis.geometricPatterns).forEach(([pattern, count]) => {
        if (count > 0) {
            const patternNodeId = `geometric-${pattern}`;
            nodes.push({
                id: patternNodeId,
                type: 'geometric_pattern',
                label: `${pattern.charAt(0).toUpperCase() + pattern.slice(1)}: ${count}`
            });
            edges.push({
                from: patternAnalysisNodeId,
                to: patternNodeId,
                relation: 'has_geometric_pattern'
            });
        }
    });
    // Nœuds pour l'analyse des stratégies
    const strategyAnalysisNodeId = 'semantic-strategy-analysis';
    nodes.push({
        id: strategyAnalysisNodeId,
        type: 'semantic_analysis',
        label: 'Strategy Analysis'
    });
    // Nœuds pour les stratégies de solution
    Object.entries(insights.strategyAnalysis.solutionStrategies).forEach(([strategy, count]) => {
        if (count > 0) {
            const strategyNodeId = `strategy-${strategy}`;
            nodes.push({
                id: strategyNodeId,
                type: 'solution_strategy',
                label: `${strategy.replace(/([A-Z])/g, ' $1').trim()}: ${count}`
            });
            edges.push({
                from: strategyAnalysisNodeId,
                to: strategyNodeId,
                relation: 'has_solution_strategy'
            });
        }
    });
    // Nœuds pour les niveaux de complexité
    Object.entries(insights.strategyAnalysis.complexityLevels).forEach(([level, count]) => {
        if (count > 0) {
            const complexityNodeId = `complexity-${level}`;
            nodes.push({
                id: complexityNodeId,
                type: 'complexity_level',
                label: `${level.charAt(0).toUpperCase() + level.slice(1)}: ${count} scenarios`
            });
            edges.push({
                from: strategyAnalysisNodeId,
                to: complexityNodeId,
                relation: 'has_complexity_level'
            });
        }
    });
    // Nœud pour les corrélations
    const correlationNodeId = 'semantic-correlations';
    nodes.push({
        id: correlationNodeId,
        type: 'semantic_analysis',
        label: `Correlations: Grid-Complexity=${insights.correlations.gridSizeVsComplexity.toFixed(2)}`
    });
}
// Fonction pour ajouter des nœuds basés sur l'analyse des actions
function addActionAnalysisNodes(nodes, edges, analysis) {
    // Nœud principal pour l'analyse des actions
    const actionAnalysisNodeId = 'action-analysis';
    nodes.push({
        id: actionAnalysisNodeId,
        type: 'action_analysis',
        label: `Action Analysis: ${analysis.actionStatistics.length} unique actions`
    });
    // Nœuds pour les statistiques d'actions importantes
    analysis.actionStatistics.slice(0, 10).forEach((actionStat) => {
        const actionNodeId = `action-stat-${actionStat.action}`;
        nodes.push({
            id: actionNodeId,
            type: 'action_statistic',
            label: `${actionStat.action}: ${actionStat.frequency} uses (${actionStat.scenarios.length} scenarios)`
        });
        edges.push({
            from: actionAnalysisNodeId,
            to: actionNodeId,
            relation: 'has_action_statistic'
        });
    });
    // Nœud spécial pour TRANSFERT
    if (analysis.transfertUsage.total > 0) {
        const transfertNodeId = 'transfert-usage';
        nodes.push({
            id: transfertNodeId,
            type: 'special_usage',
            label: `TRANSFERT: ${analysis.transfertUsage.total} scenarios (${analysis.transfertUsage.percentage.toFixed(1)}%)`
        });
        edges.push({
            from: actionAnalysisNodeId,
            to: transfertNodeId,
            relation: 'has_special_usage'
        });
    }
    // Nœud spécial pour MOTIF
    if (analysis.motifUsage.total > 0) {
        const motifNodeId = 'motif-usage';
        nodes.push({
            id: motifNodeId,
            type: 'special_usage',
            label: `MOTIF: ${analysis.motifUsage.total} scenarios (${analysis.motifUsage.percentage.toFixed(1)}%)`
        });
        edges.push({
            from: actionAnalysisNodeId,
            to: motifNodeId,
            relation: 'has_special_usage'
        });
    }
    // Nœud spécial pour COLOR
    if (analysis.colorUsage.total > 0) {
        const colorNodeId = 'color-usage';
        nodes.push({
            id: colorNodeId,
            type: 'special_usage',
            label: `COLOR: ${analysis.colorUsage.total} scenarios (${analysis.colorUsage.percentage.toFixed(1)}%)`
        });
        edges.push({
            from: actionAnalysisNodeId,
            to: colorNodeId,
            relation: 'has_special_usage'
        });
    }
    // Nœuds pour la distribution des étiquettes
    const labelDistributionNodeId = 'label-distribution';
    nodes.push({
        id: labelDistributionNodeId,
        type: 'label_analysis',
        label: `Label Distribution: ${Object.keys(analysis.labelDistribution).length} label types`
    });
    Object.entries(analysis.labelDistribution).forEach(([label, count]) => {
        const labelNodeId = `label-${label.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
        nodes.push({
            id: labelNodeId,
            type: 'scenario_label',
            label: `${label}: ${count} scenarios`
        });
        edges.push({
            from: labelDistributionNodeId,
            to: labelNodeId,
            relation: 'has_label_type'
        });
    });
    // Nœuds pour les patterns d'actions
    const actionPatternsNodeId = 'action-patterns';
    nodes.push({
        id: actionPatternsNodeId,
        type: 'pattern_analysis',
        label: `Action Patterns: ${analysis.actionPatterns.length} patterns detected`
    });
    analysis.actionPatterns.slice(0, 5).forEach((pattern, i) => {
        const patternNodeId = `action-pattern-${i}`;
        nodes.push({
            id: patternNodeId,
            type: 'action_pattern',
            label: `${pattern.label}: ${pattern.pattern.join(' → ')} (${pattern.frequency}x)`
        });
        edges.push({
            from: actionPatternsNodeId,
            to: patternNodeId,
            relation: 'has_action_pattern'
        });
    });
}
generateKnowledgeGraph();
