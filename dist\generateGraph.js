"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const parseScenarioAGI_1 = require("./utils/parseScenarioAGI");
const AGI_DIRECTORY = path_1.default.join(__dirname, '../arcdata/training');
const OUTPUT_FILE = path_1.default.join(__dirname, '../output/knowledge-graph.json');
function readAGIFiles(directory) {
    return fs_1.default.readdirSync(directory)
        .filter(file => file.endsWith('.agi'))
        .map(file => ({ file, content: fs_1.default.readFileSync(path_1.default.join(directory, file), 'utf-8') }));
}
function scenarioCommandToGraph(cmd, file, nodes, edges, parentId) {
    const nodeId = parentId || `${file}-${nodes.length}`;
    nodes.push({ id: nodeId, type: 'action', label: cmd.action, file });
    if (cmd.parameters) {
        const paramId = `${nodeId}-param`;
        nodes.push({ id: paramId, type: 'parameter', label: cmd.parameters, file });
        edges.push({ from: nodeId, to: paramId, relation: 'has_parameter' });
    }
    if (cmd.selections) {
        cmd.selections.forEach((sel, i) => {
            const selId = `${nodeId}-sel-${i}`;
            nodes.push({ id: selId, type: 'selection', label: JSON.stringify(sel), file });
            edges.push({ from: nodeId, to: selId, relation: 'has_selection' });
        });
    }
    if (cmd.specialSelection) {
        const specId = `${nodeId}-special`;
        nodes.push({ id: specId, type: 'special', label: cmd.specialSelection.type, file });
        edges.push({ from: nodeId, to: specId, relation: 'has_special_selection' });
        if ('colors' in cmd.specialSelection && cmd.specialSelection.colors) {
            const colorId = `${specId}-colors`;
            nodes.push({ id: colorId, type: 'color', label: cmd.specialSelection.colors.join(','), file });
            edges.push({ from: specId, to: colorId, relation: 'has_color' });
        }
        cmd.specialSelection.selections.forEach((sel, i) => {
            const selId = `${specId}-sel-${i}`;
            nodes.push({ id: selId, type: 'selection', label: JSON.stringify(sel), file });
            edges.push({ from: specId, to: selId, relation: 'has_selection' });
        });
    }
    if (cmd.group) {
        cmd.group.forEach((subCmd, i) => {
            scenarioCommandToGraph(subCmd, file, nodes, edges, `${nodeId}-group-${i}`);
            edges.push({ from: nodeId, to: `${nodeId}-group-${i}`, relation: 'has_group_command' });
        });
    }
}
function generateKnowledgeGraph() {
    const files = readAGIFiles(AGI_DIRECTORY);
    const nodes = [];
    const edges = [];
    files.forEach(({ file, content }) => {
        const commands = (0, parseScenarioAGI_1.parseScenarioAGIFile)(content);
        commands.forEach(cmd => scenarioCommandToGraph(cmd, file, nodes, edges));
    });
    const knowledgeGraph = { nodes, edges };
    fs_1.default.writeFileSync(OUTPUT_FILE, JSON.stringify(knowledgeGraph, null, 2));
    console.log('Knowledge graph generated successfully and saved to knowledge-graph.json');
}
generateKnowledgeGraph();
