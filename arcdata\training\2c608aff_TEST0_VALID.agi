TRANSFERT {INIT 21x19; EDIT 2 [0,0]; EDIT 2 [0,1]; EDIT 2 [0,2]; EDIT 2 [0,3]; EDIT 2 [0,4]; EDIT 2 [0,5]; EDIT 2 [0,6]; EDIT 2 [0,7]; EDIT 2 [0,8]; EDIT 2 [0,9]; EDIT 2 [0,10]; EDIT 2 [0,11]; EDIT 2 [0,12]; EDIT 2 [0,13]; EDIT 2 [0,14]; EDIT 2 [0,15]; EDIT 2 [0,16]; EDIT 2 [0,17]; EDIT 2 [0,18]; EDIT 2 [0,19]; EDIT 2 [0,20]; EDIT 2 [1,0]; EDIT 2 [1,1]; EDIT 2 [1,2]; EDIT 2 [1,3]; EDIT 2 [1,4]; EDIT 2 [1,5]; EDIT 2 [1,6]; EDIT 2 [1,7]; EDIT 2 [1,8]; EDIT 1 [1,9]; EDIT 2 [1,10]; EDIT 2 [1,11]; EDIT 2 [1,12]; EDIT 2 [1,13]; EDIT 2 [1,14]; EDIT 2 [1,15]; EDIT 2 [1,16]; EDIT 2 [1,17]; EDIT 2 [1,18]; EDIT 2 [1,19]; EDIT 2 [1,20]; EDIT 2 [2,0]; EDIT 2 [2,1]; EDIT 1 [2,2]; EDIT 2 [2,3]; EDIT 2 [2,4]; EDIT 2 [2,5]; EDIT 2 [2,6]; EDIT 2 [2,7]; EDIT 2 [2,8]; EDIT 2 [2,9]; EDIT 2 [2,10]; EDIT 2 [2,11]; EDIT 2 [2,12]; EDIT 2 [2,13]; EDIT 2 [2,14]; EDIT 2 [2,15]; EDIT 2 [2,16]; EDIT 1 [2,17]; EDIT 2 [2,18]; EDIT 2 [2,19]; EDIT 2 [2,20]; EDIT 2 [3,0]; EDIT 2 [3,1]; EDIT 2 [3,2]; EDIT 2 [3,3]; EDIT 2 [3,4]; EDIT 2 [3,5]; EDIT 2 [3,6]; EDIT 2 [3,7]; EDIT 2 [3,8]; EDIT 2 [3,9]; EDIT 2 [3,10]; EDIT 2 [3,11]; EDIT 2 [3,12]; EDIT 1 [3,13]; EDIT 2 [3,14]; EDIT 2 [3,15]; EDIT 2 [3,16]; EDIT 2 [3,17]; EDIT 2 [3,18]; EDIT 2 [3,19]; EDIT 2 [3,20]; EDIT 2 [4,0]; EDIT 2 [4,1]; EDIT 2 [4,2]; EDIT 2 [4,3]; EDIT 2 [4,4]; EDIT 2 [4,5]; EDIT 2 [4,6]; EDIT 2 [4,7]; EDIT 2 [4,8]; EDIT 2 [4,9]; EDIT 2 [4,10]; EDIT 2 [4,11]; EDIT 2 [4,12]; EDIT 2 [4,13]; EDIT 2 [4,14]; EDIT 2 [4,15]; EDIT 2 [4,16]; EDIT 2 [4,17]; EDIT 2 [4,18]; EDIT 2 [4,19]; EDIT 2 [4,20]; EDIT 2 [5,0]; EDIT 2 [5,1]; EDIT 2 [5,2]; EDIT 2 [5,3]; EDIT 2 [5,4]; EDIT 2 [5,5]; EDIT 2 [5,6]; EDIT 8 [5,7]; EDIT 8 [5,8]; EDIT 8 [5,9]; EDIT 8 [5,10]; EDIT 8 [5,11]; EDIT 8 [5,12]; EDIT 8 [5,13]; EDIT 8 [5,14]; EDIT 2 [5,15]; EDIT 2 [5,16]; EDIT 2 [5,17]; EDIT 2 [5,18]; EDIT 2 [5,19]; EDIT 2 [5,20]; EDIT 2 [6,0]; EDIT 2 [6,1]; EDIT 2 [6,2]; EDIT 2 [6,3]; EDIT 2 [6,4]; EDIT 2 [6,5]; EDIT 2 [6,6]; EDIT 8 [6,7]; EDIT 8 [6,8]; EDIT 8 [6,9]; EDIT 8 [6,10]; EDIT 8 [6,11]; EDIT 8 [6,12]; EDIT 8 [6,13]; EDIT 8 [6,14]; EDIT 2 [6,15]; EDIT 2 [6,16]; EDIT 2 [6,17]; EDIT 2 [6,18]; EDIT 2 [6,19]; EDIT 2 [6,20]; EDIT 2 [7,0]; EDIT 2 [7,1]; EDIT 2 [7,2]; EDIT 2 [7,3]; EDIT 2 [7,4]; EDIT 2 [7,5]; EDIT 2 [7,6]; EDIT 8 [7,7]; EDIT 8 [7,8]; EDIT 8 [7,9]; EDIT 8 [7,10]; EDIT 8 [7,11]; EDIT 8 [7,12]; EDIT 8 [7,13]; EDIT 8 [7,14]; EDIT 2 [7,15]; EDIT 2 [7,16]; EDIT 2 [7,17]; EDIT 2 [7,18]; EDIT 1 [7,19]; EDIT 2 [7,20]; EDIT 2 [8,0]; EDIT 2 [8,1]; EDIT 2 [8,2]; EDIT 2 [8,3]; EDIT 2 [8,4]; EDIT 2 [8,5]; EDIT 2 [8,6]; EDIT 8 [8,7]; EDIT 8 [8,8]; EDIT 8 [8,9]; EDIT 8 [8,10]; EDIT 8 [8,11]; EDIT 8 [8,12]; EDIT 8 [8,13]; EDIT 8 [8,14]; EDIT 2 [8,15]; EDIT 2 [8,16]; EDIT 2 [8,17]; EDIT 2 [8,18]; EDIT 2 [8,19]; EDIT 2 [8,20]; EDIT 2 [9,0]; EDIT 2 [9,1]; EDIT 1 [9,2]; EDIT 2 [9,3]; EDIT 2 [9,4]; EDIT 2 [9,5]; EDIT 2 [9,6]; EDIT 8 [9,7]; EDIT 8 [9,8]; EDIT 8 [9,9]; EDIT 8 [9,10]; EDIT 8 [9,11]; EDIT 8 [9,12]; EDIT 8 [9,13]; EDIT 8 [9,14]; EDIT 2 [9,15]; EDIT 2 [9,16]; EDIT 2 [9,17]; EDIT 2 [9,18]; EDIT 2 [9,19]; EDIT 2 [9,20]; EDIT 2 [10,0]; EDIT 2 [10,1]; EDIT 2 [10,2]; EDIT 2 [10,3]; EDIT 2 [10,4]; EDIT 2 [10,5]; EDIT 2 [10,6]; EDIT 8 [10,7]; EDIT 8 [10,8]; EDIT 8 [10,9]; EDIT 8 [10,10]; EDIT 8 [10,11]; EDIT 8 [10,12]; EDIT 8 [10,13]; EDIT 8 [10,14]; EDIT 2 [10,15]; EDIT 2 [10,16]; EDIT 2 [10,17]; EDIT 2 [10,18]; EDIT 2 [10,19]; EDIT 2 [10,20]; EDIT 2 [11,0]; EDIT 2 [11,1]; EDIT 2 [11,2]; EDIT 2 [11,3]; EDIT 2 [11,4]; EDIT 2 [11,5]; EDIT 2 [11,6]; EDIT 2 [11,7]; EDIT 2 [11,8]; EDIT 2 [11,9]; EDIT 2 [11,10]; EDIT 2 [11,11]; EDIT 2 [11,12]; EDIT 2 [11,13]; EDIT 2 [11,14]; EDIT 2 [11,15]; EDIT 2 [11,16]; EDIT 2 [11,17]; EDIT 2 [11,18]; EDIT 2 [11,19]; EDIT 2 [11,20]; EDIT 2 [12,0]; EDIT 2 [12,1]; EDIT 2 [12,2]; EDIT 2 [12,3]; EDIT 2 [12,4]; EDIT 2 [12,5]; EDIT 2 [12,6]; EDIT 2 [12,7]; EDIT 2 [12,8]; EDIT 2 [12,9]; EDIT 2 [12,10]; EDIT 2 [12,11]; EDIT 2 [12,12]; EDIT 2 [12,13]; EDIT 2 [12,14]; EDIT 2 [12,15]; EDIT 2 [12,16]; EDIT 2 [12,17]; EDIT 2 [12,18]; EDIT 2 [12,19]; EDIT 2 [12,20]; EDIT 2 [13,0]; EDIT 2 [13,1]; EDIT 2 [13,2]; EDIT 2 [13,3]; EDIT 2 [13,4]; EDIT 2 [13,5]; EDIT 2 [13,6]; EDIT 2 [13,7]; EDIT 2 [13,8]; EDIT 2 [13,9]; EDIT 2 [13,10]; EDIT 2 [13,11]; EDIT 2 [13,12]; EDIT 2 [13,13]; EDIT 2 [13,14]; EDIT 1 [13,15]; EDIT 2 [13,16]; EDIT 2 [13,17]; EDIT 2 [13,18]; EDIT 2 [13,19]; EDIT 2 [13,20]; EDIT 2 [14,0]; EDIT 2 [14,1]; EDIT 2 [14,2]; EDIT 2 [14,3]; EDIT 2 [14,4]; EDIT 2 [14,5]; EDIT 2 [14,6]; EDIT 2 [14,7]; EDIT 2 [14,8]; EDIT 2 [14,9]; EDIT 2 [14,10]; EDIT 2 [14,11]; EDIT 2 [14,12]; EDIT 2 [14,13]; EDIT 2 [14,14]; EDIT 2 [14,15]; EDIT 2 [14,16]; EDIT 2 [14,17]; EDIT 2 [14,18]; EDIT 2 [14,19]; EDIT 2 [14,20]; EDIT 2 [15,0]; EDIT 2 [15,1]; EDIT 2 [15,2]; EDIT 2 [15,3]; EDIT 2 [15,4]; EDIT 2 [15,5]; EDIT 2 [15,6]; EDIT 2 [15,7]; EDIT 2 [15,8]; EDIT 2 [15,9]; EDIT 2 [15,10]; EDIT 2 [15,11]; EDIT 2 [15,12]; EDIT 2 [15,13]; EDIT 2 [15,14]; EDIT 2 [15,15]; EDIT 2 [15,16]; EDIT 2 [15,17]; EDIT 2 [15,18]; EDIT 2 [15,19]; EDIT 2 [15,20]; EDIT 2 [16,0]; EDIT 2 [16,1]; EDIT 2 [16,2]; EDIT 2 [16,3]; EDIT 2 [16,4]; EDIT 2 [16,5]; EDIT 2 [16,6]; EDIT 2 [16,7]; EDIT 2 [16,8]; EDIT 2 [16,9]; EDIT 2 [16,10]; EDIT 2 [16,11]; EDIT 2 [16,12]; EDIT 2 [16,13]; EDIT 2 [16,14]; EDIT 2 [16,15]; EDIT 2 [16,16]; EDIT 2 [16,17]; EDIT 2 [16,18]; EDIT 2 [16,19]; EDIT 2 [16,20]; EDIT 2 [17,0]; EDIT 2 [17,1]; EDIT 2 [17,2]; EDIT 1 [17,3]; EDIT 2 [17,4]; EDIT 2 [17,5]; EDIT 2 [17,6]; EDIT 2 [17,7]; EDIT 2 [17,8]; EDIT 2 [17,9]; EDIT 2 [17,10]; EDIT 1 [17,11]; EDIT 2 [17,12]; EDIT 2 [17,13]; EDIT 2 [17,14]; EDIT 2 [17,15]; EDIT 2 [17,16]; EDIT 2 [17,17]; EDIT 2 [17,18]; EDIT 2 [17,19]; EDIT 2 [17,20]; EDIT 2 [18,0]; EDIT 2 [18,1]; EDIT 2 [18,2]; EDIT 2 [18,3]; EDIT 2 [18,4]; EDIT 2 [18,5]; EDIT 2 [18,6]; EDIT 2 [18,7]; EDIT 2 [18,8]; EDIT 2 [18,9]; EDIT 2 [18,10]; EDIT 2 [18,11]; EDIT 2 [18,12]; EDIT 2 [18,13]; EDIT 2 [18,14]; EDIT 2 [18,15]; EDIT 2 [18,16]; EDIT 2 [18,17]; EDIT 2 [18,18]; EDIT 2 [18,19]; EDIT 2 [18,20]}
EDITS {EDIT 1 [2,9]; EDIT 1 [3,9]; EDIT 1 [4,9]; EDIT 1 [4,13]; EDIT 1 [7,18]; EDIT 1 [7,17]; EDIT 1 [7,16]; EDIT 1 [7,15]; EDIT 1 [16,11]; EDIT 1 [15,11]; EDIT 1 [13,11]; EDIT 1 [12,11]; EDIT 1 [11,11]; EDIT 1 [14,11]; EDIT 1 [9,3]; EDIT 1 [9,4]; EDIT 1 [9,5]; EDIT 1 [9,6]}
END