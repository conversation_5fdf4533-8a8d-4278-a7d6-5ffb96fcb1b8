TRANSFERT {INIT 11x11; EDIT 6 [0,0]; EDIT 6 [0,1]; EDIT 6 [0,2]; EDIT 6 [0,3]; EDIT 6 [0,4]; EDIT 6 [0,5]; EDIT 6 [0,6]; EDIT 6 [0,7]; EDIT 6 [0,8]; EDIT 6 [0,9]; EDIT 6 [0,10]; EDIT 6 [1,0]; EDIT 6 [1,1]; EDIT 6 [1,2]; EDIT 6 [1,3]; EDIT 6 [1,4]; EDIT 6 [1,5]; EDIT 6 [1,6]; EDIT 6 [1,7]; EDIT 6 [1,8]; EDIT 6 [1,9]; EDIT 6 [1,10]; EDIT 6 [2,0]; EDIT 6 [2,1]; EDIT 6 [2,2]; EDIT 6 [2,3]; EDIT 6 [2,4]; EDIT 6 [2,5]; EDIT 6 [2,6]; EDIT 6 [2,7]; EDIT 6 [2,8]; EDIT 6 [2,9]; EDIT 6 [2,10]; EDIT 6 [3,0]; EDIT 6 [3,1]; EDIT 6 [3,2]; EDIT 6 [3,3]; EDIT 6 [3,4]; EDIT 6 [3,5]; EDIT 6 [3,6]; EDIT 6 [3,7]; EDIT 6 [3,8]; EDIT 6 [3,9]; EDIT 6 [3,10]; EDIT 6 [4,0]; EDIT 6 [4,1]; EDIT 6 [4,2]; EDIT 6 [4,3]; EDIT 6 [4,4]; EDIT 6 [4,5]; EDIT 6 [4,6]; EDIT 6 [4,7]; EDIT 6 [4,8]; EDIT 6 [4,9]; EDIT 6 [4,10]; EDIT 6 [5,0]; EDIT 6 [5,1]; EDIT 6 [5,2]; EDIT 6 [5,3]; EDIT 6 [5,4]; EDIT 6 [5,6]; EDIT 6 [5,7]; EDIT 6 [5,8]; EDIT 6 [5,9]; EDIT 6 [5,10]; EDIT 6 [6,0]; EDIT 6 [6,1]; EDIT 6 [6,2]; EDIT 6 [6,3]; EDIT 6 [6,4]; EDIT 6 [6,5]; EDIT 6 [6,6]; EDIT 6 [6,7]; EDIT 6 [6,8]; EDIT 6 [6,9]; EDIT 6 [6,10]; EDIT 6 [7,0]; EDIT 6 [7,1]; EDIT 6 [7,2]; EDIT 6 [7,3]; EDIT 6 [7,4]; EDIT 6 [7,5]; EDIT 6 [7,6]; EDIT 6 [7,7]; EDIT 6 [7,8]; EDIT 6 [7,9]; EDIT 6 [7,10]; EDIT 6 [8,0]; EDIT 6 [8,1]; EDIT 6 [8,2]; EDIT 6 [8,3]; EDIT 6 [8,4]; EDIT 6 [8,5]; EDIT 6 [8,6]; EDIT 6 [8,7]; EDIT 6 [8,8]; EDIT 6 [8,9]; EDIT 6 [8,10]; EDIT 6 [9,0]; EDIT 6 [9,1]; EDIT 6 [9,2]; EDIT 6 [9,3]; EDIT 6 [9,4]; EDIT 6 [9,5]; EDIT 6 [9,6]; EDIT 6 [9,7]; EDIT 6 [9,8]; EDIT 6 [9,9]; EDIT 6 [9,10]; EDIT 6 [10,0]; EDIT 6 [10,1]; EDIT 6 [10,2]; EDIT 6 [10,3]; EDIT 6 [10,4]; EDIT 6 [10,5]; EDIT 6 [10,6]; EDIT 6 [10,7]; EDIT 6 [10,8]; EDIT 6 [10,9]; EDIT 6 [10,10]}
EDITS {EDIT 0 [4,6]; EDIT 0 [3,7]; EDIT 0 [2,8]; EDIT 0 [1,9]; EDIT 0 [0,10]; EDIT 0 [6,6]; EDIT 0 [7,7]; EDIT 0 [8,8]; EDIT 0 [9,9]; EDIT 0 [10,10]; EDIT 0 [6,4]; EDIT 0 [7,3]; EDIT 0 [8,2]; EDIT 0 [9,1]; EDIT 0 [10,0]; EDIT 0 [4,4]; EDIT 0 [3,3]; EDIT 0 [2,2]; EDIT 0 [1,1]; EDIT 0 [0,0]}
END