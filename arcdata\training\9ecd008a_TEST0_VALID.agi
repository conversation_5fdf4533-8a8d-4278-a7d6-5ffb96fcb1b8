TRANSFERT {INIT 16x16; EDIT 4 [0,0]; EDIT 8 [0,1]; EDIT 9 [0,2]; EDIT 9 [0,3]; EDIT 6 [0,4]; EDIT 6 [0,5]; EDIT 5 [0,6]; EDIT 1 [0,7]; EDIT 1 [0,8]; EDIT 5 [0,9]; EDIT 6 [0,10]; EDIT 6 [0,11]; EDIT 9 [0,12]; EDIT 9 [0,13]; EDIT 8 [0,14]; EDIT 4 [0,15]; EDIT 8 [1,0]; EDIT 6 [1,1]; EDIT 9 [1,2]; EDIT 9 [1,3]; EDIT 6 [1,4]; EDIT 7 [1,5]; EDIT 1 [1,6]; EDIT 5 [1,7]; EDIT 5 [1,8]; EDIT 1 [1,9]; EDIT 7 [1,10]; EDIT 6 [1,11]; EDIT 9 [1,12]; EDIT 9 [1,13]; EDIT 6 [1,14]; EDIT 8 [1,15]; EDIT 9 [2,0]; EDIT 9 [2,1]; EDIT 5 [2,2]; EDIT 2 [2,3]; EDIT 5 [2,4]; EDIT 1 [2,5]; EDIT 5 [2,6]; EDIT 5 [2,7]; EDIT 5 [2,8]; EDIT 5 [2,9]; EDIT 1 [2,10]; EDIT 5 [2,11]; EDIT 2 [2,12]; EDIT 5 [2,13]; EDIT 9 [2,14]; EDIT 9 [2,15]; EDIT 9 [3,0]; EDIT 9 [3,1]; EDIT 2 [3,2]; EDIT 2 [3,3]; EDIT 1 [3,4]; EDIT 5 [3,5]; EDIT 5 [3,6]; EDIT 9 [3,7]; EDIT 9 [3,8]; EDIT 5 [3,9]; EDIT 5 [3,10]; EDIT 1 [3,11]; EDIT 2 [3,12]; EDIT 2 [3,13]; EDIT 9 [3,14]; EDIT 9 [3,15]; EDIT 6 [4,0]; EDIT 6 [4,1]; EDIT 5 [4,2]; EDIT 1 [4,3]; EDIT 1 [4,4]; EDIT 4 [4,5]; EDIT 5 [4,6]; EDIT 2 [4,7]; EDIT 2 [4,8]; EDIT 5 [4,9]; EDIT 4 [4,10]; EDIT 1 [4,11]; EDIT 1 [4,12]; EDIT 5 [4,13]; EDIT 6 [4,14]; EDIT 6 [4,15]; EDIT 6 [5,0]; EDIT 4 [5,4]; EDIT 4 [5,5]; EDIT 2 [5,6]; EDIT 7 [5,7]; EDIT 7 [5,8]; EDIT 2 [5,9]; EDIT 4 [5,10]; EDIT 4 [5,11]; EDIT 5 [5,12]; EDIT 1 [5,13]; EDIT 7 [5,14]; EDIT 6 [5,15]; EDIT 5 [6,0]; EDIT 5 [6,4]; EDIT 2 [6,5]; EDIT 9 [6,6]; EDIT 5 [6,7]; EDIT 5 [6,8]; EDIT 9 [6,9]; EDIT 2 [6,10]; EDIT 5 [6,11]; EDIT 5 [6,12]; EDIT 5 [6,13]; EDIT 1 [6,14]; EDIT 5 [6,15]; EDIT 1 [7,0]; EDIT 2 [7,4]; EDIT 7 [7,5]; EDIT 5 [7,6]; EDIT 9 [7,7]; EDIT 9 [7,8]; EDIT 5 [7,9]; EDIT 7 [7,10]; EDIT 2 [7,11]; EDIT 9 [7,12]; EDIT 5 [7,13]; EDIT 5 [7,14]; EDIT 1 [7,15]; EDIT 1 [8,0]; EDIT 5 [8,1]; EDIT 5 [8,2]; EDIT 9 [8,3]; EDIT 2 [8,4]; EDIT 7 [8,5]; EDIT 5 [8,6]; EDIT 9 [8,7]; EDIT 9 [8,8]; EDIT 5 [8,9]; EDIT 7 [8,10]; EDIT 2 [8,11]; EDIT 9 [8,12]; EDIT 5 [8,13]; EDIT 5 [8,14]; EDIT 1 [8,15]; EDIT 5 [9,0]; EDIT 1 [9,1]; EDIT 5 [9,2]; EDIT 5 [9,3]; EDIT 5 [9,4]; EDIT 2 [9,5]; EDIT 9 [9,6]; EDIT 5 [9,7]; EDIT 5 [9,8]; EDIT 9 [9,9]; EDIT 2 [9,10]; EDIT 5 [9,11]; EDIT 5 [9,12]; EDIT 5 [9,13]; EDIT 1 [9,14]; EDIT 5 [9,15]; EDIT 6 [10,0]; EDIT 7 [10,1]; EDIT 1 [10,2]; EDIT 5 [10,3]; EDIT 4 [10,4]; EDIT 4 [10,5]; EDIT 2 [10,6]; EDIT 7 [10,7]; EDIT 7 [10,8]; EDIT 2 [10,9]; EDIT 4 [10,10]; EDIT 4 [10,11]; EDIT 5 [10,12]; EDIT 1 [10,13]; EDIT 7 [10,14]; EDIT 6 [10,15]; EDIT 6 [11,0]; EDIT 6 [11,1]; EDIT 5 [11,2]; EDIT 1 [11,3]; EDIT 1 [11,4]; EDIT 4 [11,5]; EDIT 5 [11,6]; EDIT 2 [11,7]; EDIT 2 [11,8]; EDIT 5 [11,9]; EDIT 4 [11,10]; EDIT 1 [11,11]; EDIT 1 [11,12]; EDIT 5 [11,13]; EDIT 6 [11,14]; EDIT 6 [11,15]; EDIT 9 [12,0]; EDIT 9 [12,1]; EDIT 2 [12,2]; EDIT 2 [12,3]; EDIT 1 [12,4]; EDIT 5 [12,5]; EDIT 5 [12,6]; EDIT 9 [12,7]; EDIT 9 [12,8]; EDIT 5 [12,9]; EDIT 5 [12,10]; EDIT 1 [12,11]; EDIT 2 [12,12]; EDIT 2 [12,13]; EDIT 9 [12,14]; EDIT 9 [12,15]; EDIT 9 [13,0]; EDIT 9 [13,1]; EDIT 5 [13,2]; EDIT 2 [13,3]; EDIT 5 [13,4]; EDIT 1 [13,5]; EDIT 5 [13,6]; EDIT 5 [13,7]; EDIT 5 [13,8]; EDIT 5 [13,9]; EDIT 1 [13,10]; EDIT 5 [13,11]; EDIT 2 [13,12]; EDIT 5 [13,13]; EDIT 9 [13,14]; EDIT 9 [13,15]; EDIT 8 [14,0]; EDIT 6 [14,1]; EDIT 9 [14,2]; EDIT 9 [14,3]; EDIT 6 [14,4]; EDIT 7 [14,5]; EDIT 1 [14,6]; EDIT 5 [14,7]; EDIT 5 [14,8]; EDIT 1 [14,9]; EDIT 7 [14,10]; EDIT 6 [14,11]; EDIT 9 [14,12]; EDIT 9 [14,13]; EDIT 6 [14,14]; EDIT 8 [14,15]; EDIT 4 [15,0]; EDIT 8 [15,1]; EDIT 9 [15,2]; EDIT 9 [15,3]; EDIT 6 [15,4]; EDIT 6 [15,5]; EDIT 5 [15,6]; EDIT 1 [15,7]; EDIT 1 [15,8]; EDIT 5 [15,9]; EDIT 6 [15,10]; EDIT 6 [15,11]; EDIT 9 [15,12]; EDIT 9 [15,13]; EDIT 8 [15,14]; EDIT 4 [15,15]}
EXTRACT [5,12 7,14]
CUT [0,0 2,2]
FLIP HORIZONTAL [0,0 2,2]
PASTE [0,0]
END