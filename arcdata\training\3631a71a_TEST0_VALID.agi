TRANSFERT {INIT 30x30; EDIT 8 [0,0]; EDIT 7 [0,2]; EDIT 7 [0,4]; EDIT 7 [0,5]; EDIT 1 [0,6]; EDIT 1 [0,7]; EDIT 3 [0,9]; EDIT 6 [0,11]; EDIT 8 [0,13]; EDIT 8 [0,18]; EDIT 6 [0,20]; EDIT 3 [0,22]; EDIT 1 [0,24]; EDIT 1 [0,25]; EDIT 7 [0,26]; EDIT 7 [0,27]; EDIT 7 [0,29]; EDIT 8 [1,1]; EDIT 7 [1,4]; EDIT 7 [1,5]; EDIT 1 [1,6]; EDIT 1 [1,7]; EDIT 3 [1,8]; EDIT 3 [1,9]; EDIT 6 [1,10]; EDIT 6 [1,11]; EDIT 8 [1,12]; EDIT 8 [1,13]; EDIT 8 [1,18]; EDIT 8 [1,19]; EDIT 6 [1,20]; EDIT 6 [1,21]; EDIT 3 [1,22]; EDIT 3 [1,23]; EDIT 1 [1,24]; EDIT 1 [1,25]; EDIT 7 [1,26]; EDIT 7 [1,27]; EDIT 9 [2,0]; EDIT 9 [2,1]; EDIT 9 [2,2]; EDIT 9 [2,3]; EDIT 9 [2,4]; EDIT 9 [2,5]; EDIT 9 [2,6]; EDIT 8 [2,7]; EDIT 6 [2,9]; EDIT 7 [2,10]; EDIT 7 [2,11]; EDIT 6 [2,15]; EDIT 6 [2,16]; EDIT 7 [2,20]; EDIT 7 [2,21]; EDIT 6 [2,22]; EDIT 8 [2,24]; EDIT 1 [2,26]; EDIT 1 [2,27]; EDIT 2 [2,29]; EDIT 9 [3,0]; EDIT 9 [3,1]; EDIT 9 [3,2]; EDIT 9 [3,3]; EDIT 9 [3,4]; EDIT 9 [3,5]; EDIT 9 [3,6]; EDIT 6 [3,8]; EDIT 6 [3,9]; EDIT 7 [3,10]; EDIT 7 [3,11]; EDIT 6 [3,14]; EDIT 6 [3,17]; EDIT 9 [3,20]; EDIT 9 [3,21]; EDIT 9 [3,22]; EDIT 9 [3,23]; EDIT 9 [3,24]; EDIT 8 [3,25]; EDIT 1 [3,26]; EDIT 1 [3,27]; EDIT 9 [4,0]; EDIT 9 [4,1]; EDIT 9 [4,2]; EDIT 9 [4,3]; EDIT 9 [4,4]; EDIT 9 [4,5]; EDIT 9 [4,6]; EDIT 6 [4,7]; EDIT 8 [4,9]; EDIT 6 [4,12]; EDIT 6 [4,13]; EDIT 6 [4,18]; EDIT 6 [4,19]; EDIT 9 [4,20]; EDIT 9 [4,21]; EDIT 9 [4,22]; EDIT 9 [4,23]; EDIT 9 [4,24]; EDIT 1 [4,28]; EDIT 1 [4,29]; EDIT 7 [5,0]; EDIT 7 [5,1]; EDIT 1 [5,2]; EDIT 1 [5,3]; EDIT 5 [5,5]; EDIT 6 [5,6]; EDIT 6 [5,7]; EDIT 8 [5,8]; EDIT 8 [5,9]; EDIT 6 [5,12]; EDIT 6 [5,13]; EDIT 6 [5,18]; EDIT 6 [5,19]; EDIT 9 [5,20]; EDIT 9 [5,21]; EDIT 9 [5,22]; EDIT 9 [5,23]; EDIT 9 [5,24]; EDIT 6 [5,25]; EDIT 5 [5,26]; EDIT 1 [5,28]; EDIT 1 [5,29]; EDIT 1 [6,0]; EDIT 1 [6,1]; EDIT 8 [6,3]; EDIT 6 [6,5]; EDIT 2 [6,6]; EDIT 6 [6,11]; EDIT 5 [6,14]; EDIT 5 [6,15]; EDIT 5 [6,16]; EDIT 5 [6,17]; EDIT 9 [6,20]; EDIT 9 [6,21]; EDIT 9 [6,22]; EDIT 9 [6,23]; EDIT 9 [6,24]; EDIT 2 [6,25]; EDIT 6 [6,26]; EDIT 8 [6,28]; EDIT 1 [7,0]; EDIT 1 [7,1]; EDIT 8 [7,2]; EDIT 6 [7,4]; EDIT 6 [7,5]; EDIT 2 [7,7]; EDIT 6 [7,10]; EDIT 5 [7,14]; EDIT 5 [7,17]; EDIT 9 [7,20]; EDIT 9 [7,21]; EDIT 9 [7,22]; EDIT 9 [7,23]; EDIT 9 [7,24]; EDIT 9 [7,25]; EDIT 9 [7,26]; EDIT 6 [7,27]; EDIT 8 [7,29]; EDIT 3 [8,1]; EDIT 6 [8,3]; EDIT 8 [8,5]; EDIT 6 [8,12]; EDIT 3 [8,14]; EDIT 3 [8,17]; EDIT 6 [8,19]; EDIT 9 [8,22]; EDIT 9 [8,23]; EDIT 9 [8,24]; EDIT 9 [8,25]; EDIT 9 [8,26]; EDIT 6 [8,28]; EDIT 3 [9,0]; EDIT 3 [9,1]; EDIT 6 [9,2]; EDIT 6 [9,3]; EDIT 8 [9,4]; EDIT 8 [9,5]; EDIT 6 [9,9]; EDIT 5 [9,11]; EDIT 3 [9,15]; EDIT 3 [9,16]; EDIT 5 [9,20]; EDIT 9 [9,22]; EDIT 9 [9,23]; EDIT 9 [9,24]; EDIT 9 [9,25]; EDIT 9 [9,26]; EDIT 8 [9,27]; EDIT 6 [9,28]; EDIT 6 [9,29]; EDIT 6 [10,1]; EDIT 7 [10,2]; EDIT 7 [10,3]; EDIT 6 [10,7]; EDIT 3 [10,12]; EDIT 6 [10,15]; EDIT 6 [10,16]; EDIT 3 [10,19]; EDIT 9 [10,22]; EDIT 9 [10,23]; EDIT 9 [10,24]; EDIT 9 [10,25]; EDIT 9 [10,26]; EDIT 7 [10,28]; EDIT 7 [10,29]; EDIT 6 [11,0]; EDIT 6 [11,1]; EDIT 7 [11,2]; EDIT 7 [11,3]; EDIT 6 [11,6]; EDIT 5 [11,9]; EDIT 3 [11,13]; EDIT 6 [11,14]; EDIT 6 [11,15]; EDIT 6 [11,16]; EDIT 6 [11,17]; EDIT 3 [11,18]; EDIT 9 [11,22]; EDIT 9 [11,23]; EDIT 9 [11,24]; EDIT 9 [11,25]; EDIT 9 [11,26]; EDIT 7 [11,28]; EDIT 7 [11,29]; EDIT 8 [12,1]; EDIT 6 [12,4]; EDIT 6 [12,5]; EDIT 6 [12,8]; EDIT 3 [12,10]; EDIT 4 [12,13]; EDIT 3 [12,14]; EDIT 3 [12,17]; EDIT 4 [12,18]; EDIT 3 [12,21]; EDIT 6 [12,23]; EDIT 6 [12,26]; EDIT 6 [12,27]; EDIT 8 [13,0]; EDIT 8 [13,1]; EDIT 6 [13,4]; EDIT 6 [13,5]; EDIT 3 [13,11]; EDIT 4 [13,12]; EDIT 4 [13,19]; EDIT 3 [13,20]; EDIT 6 [13,26]; EDIT 6 [13,27]; EDIT 6 [14,3]; EDIT 5 [14,6]; EDIT 5 [14,7]; EDIT 3 [14,8]; EDIT 6 [14,11]; EDIT 3 [14,12]; EDIT 2 [14,14]; EDIT 2 [14,17]; EDIT 3 [14,19]; EDIT 6 [14,20]; EDIT 3 [14,23]; EDIT 5 [14,24]; EDIT 5 [14,25]; EDIT 6 [14,28]; EDIT 6 [15,2]; EDIT 5 [15,6]; EDIT 3 [15,9]; EDIT 6 [15,10]; EDIT 6 [15,11]; EDIT 2 [15,15]; EDIT 2 [15,16]; EDIT 6 [15,20]; EDIT 6 [15,21]; EDIT 3 [15,22]; EDIT 5 [15,25]; EDIT 6 [15,29]; EDIT 6 [16,2]; EDIT 5 [16,6]; EDIT 3 [16,9]; EDIT 6 [16,10]; EDIT 6 [16,11]; EDIT 2 [16,15]; EDIT 2 [16,16]; EDIT 6 [16,20]; EDIT 6 [16,21]; EDIT 3 [16,22]; EDIT 5 [16,25]; EDIT 6 [16,29]; EDIT 6 [17,3]; EDIT 5 [17,6]; EDIT 5 [17,7]; EDIT 3 [17,8]; EDIT 6 [17,11]; EDIT 3 [17,12]; EDIT 2 [17,14]; EDIT 2 [17,17]; EDIT 3 [17,19]; EDIT 6 [17,20]; EDIT 3 [17,23]; EDIT 5 [17,24]; EDIT 5 [17,25]; EDIT 6 [17,28]; EDIT 8 [18,0]; EDIT 8 [18,1]; EDIT 6 [18,4]; EDIT 6 [18,5]; EDIT 3 [18,11]; EDIT 4 [18,12]; EDIT 4 [18,19]; EDIT 3 [18,20]; EDIT 6 [18,26]; EDIT 6 [18,27]; EDIT 8 [19,1]; EDIT 6 [19,4]; EDIT 6 [19,5]; EDIT 6 [19,8]; EDIT 3 [19,10]; EDIT 4 [19,13]; EDIT 3 [19,14]; EDIT 3 [19,17]; EDIT 4 [19,18]; EDIT 3 [19,21]; EDIT 6 [19,23]; EDIT 6 [19,26]; EDIT 6 [19,27]; EDIT 6 [20,0]; EDIT 6 [20,1]; EDIT 7 [20,2]; EDIT 7 [20,3]; EDIT 6 [20,6]; EDIT 5 [20,9]; EDIT 3 [20,13]; EDIT 6 [20,14]; EDIT 6 [20,15]; EDIT 6 [20,16]; EDIT 6 [20,17]; EDIT 3 [20,18]; EDIT 5 [20,22]; EDIT 6 [20,25]; EDIT 7 [20,28]; EDIT 7 [20,29]; EDIT 6 [21,1]; EDIT 7 [21,2]; EDIT 7 [21,3]; EDIT 6 [21,7]; EDIT 3 [21,12]; EDIT 6 [21,15]; EDIT 6 [21,16]; EDIT 3 [21,19]; EDIT 6 [21,24]; EDIT 7 [21,28]; EDIT 7 [21,29]; EDIT 3 [22,0]; EDIT 3 [22,1]; EDIT 6 [22,2]; EDIT 6 [22,3]; EDIT 8 [22,4]; EDIT 8 [22,5]; EDIT 6 [22,9]; EDIT 5 [22,11]; EDIT 3 [22,15]; EDIT 3 [22,16]; EDIT 5 [22,20]; EDIT 6 [22,22]; EDIT 8 [22,26]; EDIT 8 [22,27]; EDIT 6 [22,28]; EDIT 6 [22,29]; EDIT 3 [23,1]; EDIT 6 [23,3]; EDIT 8 [23,5]; EDIT 6 [23,12]; EDIT 3 [23,14]; EDIT 3 [23,17]; EDIT 6 [23,19]; EDIT 8 [23,26]; EDIT 6 [23,28]; EDIT 1 [24,0]; EDIT 1 [24,1]; EDIT 8 [24,2]; EDIT 6 [24,4]; EDIT 6 [24,5]; EDIT 2 [24,7]; EDIT 6 [24,10]; EDIT 5 [24,14]; EDIT 5 [24,17]; EDIT 6 [24,21]; EDIT 2 [24,24]; EDIT 6 [24,26]; EDIT 6 [24,27]; EDIT 8 [24,29]; EDIT 1 [25,0]; EDIT 1 [25,1]; EDIT 8 [25,3]; EDIT 6 [25,5]; EDIT 2 [25,6]; EDIT 6 [25,11]; EDIT 5 [25,14]; EDIT 5 [25,15]; EDIT 5 [25,16]; EDIT 5 [25,17]; EDIT 6 [25,20]; EDIT 2 [25,25]; EDIT 6 [25,26]; EDIT 8 [25,28]; EDIT 7 [26,0]; EDIT 7 [26,1]; EDIT 1 [26,2]; EDIT 1 [26,3]; EDIT 5 [26,5]; EDIT 6 [26,6]; EDIT 6 [26,7]; EDIT 8 [26,8]; EDIT 8 [26,9]; EDIT 6 [26,12]; EDIT 6 [26,13]; EDIT 6 [26,18]; EDIT 6 [26,19]; EDIT 8 [26,22]; EDIT 8 [26,23]; EDIT 6 [26,24]; EDIT 6 [26,25]; EDIT 5 [26,26]; EDIT 1 [26,28]; EDIT 1 [26,29]; EDIT 7 [27,0]; EDIT 7 [27,1]; EDIT 1 [27,2]; EDIT 1 [27,3]; EDIT 6 [27,7]; EDIT 8 [27,9]; EDIT 6 [27,12]; EDIT 6 [27,13]; EDIT 9 [27,18]; EDIT 9 [27,19]; EDIT 9 [27,20]; EDIT 9 [27,21]; EDIT 9 [27,22]; EDIT 9 [27,23]; EDIT 9 [27,24]; EDIT 1 [27,28]; EDIT 1 [27,29]; EDIT 1 [28,4]; EDIT 1 [28,5]; EDIT 8 [28,6]; EDIT 6 [28,8]; EDIT 6 [28,9]; EDIT 7 [28,10]; EDIT 7 [28,11]; EDIT 6 [28,14]; EDIT 6 [28,17]; EDIT 9 [28,18]; EDIT 9 [28,19]; EDIT 9 [28,20]; EDIT 9 [28,21]; EDIT 9 [28,22]; EDIT 9 [28,23]; EDIT 9 [28,24]; EDIT 8 [28,25]; EDIT 1 [28,26]; EDIT 1 [28,27]; EDIT 7 [29,0]; EDIT 2 [29,2]; EDIT 1 [29,4]; EDIT 1 [29,5]; EDIT 8 [29,7]; EDIT 6 [29,9]; EDIT 7 [29,10]; EDIT 7 [29,11]; EDIT 6 [29,15]; EDIT 6 [29,16]; EDIT 7 [29,20]; EDIT 7 [29,21]; EDIT 6 [29,22]; EDIT 8 [29,24]; EDIT 1 [29,26]; EDIT 1 [29,27]; EDIT 2 [29,29]}
COPY [27,7 28,13]
FLIP HORIZONTAL [27,7 28,13]
PASTE [27,18]
COPY [27,0 29,6]
FLIP VERTICAL [27,0 29,6]
PASTE [2,0]
COPY [3,7 7,11]
FLIP HORIZONTAL [3,7 7,11]
PASTE [3,20]
COPY [7,5 11,9]
FLIP HORIZONTAL [7,5 11,9]
PASTE [7,22]
END