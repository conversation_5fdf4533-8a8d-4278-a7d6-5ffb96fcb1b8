TRANSFERT {INIT 18x18; EDIT 1 [0,0]; EDIT 1 [0,1]; EDIT 1 [0,2]; EDIT 1 [0,3]; EDIT 1 [0,4]; EDIT 1 [0,5]; EDIT 1 [0,6]; EDIT 1 [0,7]; EDIT 1 [0,8]; EDIT 1 [0,9]; EDIT 1 [0,10]; EDIT 1 [0,11]; EDIT 1 [0,12]; EDIT 1 [0,16]; EDIT 1 [0,17]; EDIT 1 [1,0]; EDIT 2 [1,1]; EDIT 3 [1,2]; EDIT 4 [1,3]; EDIT 5 [1,4]; EDIT 6 [1,5]; EDIT 7 [1,6]; EDIT 8 [1,7]; EDIT 9 [1,8]; EDIT 1 [1,9]; EDIT 2 [1,10]; EDIT 3 [1,11]; EDIT 4 [1,12]; EDIT 8 [1,16]; EDIT 9 [1,17]; EDIT 1 [2,0]; EDIT 3 [2,1]; EDIT 5 [2,2]; EDIT 7 [2,3]; EDIT 9 [2,4]; EDIT 2 [2,5]; EDIT 4 [2,6]; EDIT 6 [2,7]; EDIT 8 [2,8]; EDIT 1 [2,9]; EDIT 3 [2,10]; EDIT 5 [2,11]; EDIT 7 [2,12]; EDIT 6 [2,16]; EDIT 8 [2,17]; EDIT 1 [3,0]; EDIT 4 [3,1]; EDIT 7 [3,2]; EDIT 1 [3,3]; EDIT 4 [3,4]; EDIT 7 [3,5]; EDIT 1 [3,6]; EDIT 4 [3,7]; EDIT 7 [3,8]; EDIT 1 [3,9]; EDIT 4 [3,10]; EDIT 7 [3,11]; EDIT 1 [3,12]; EDIT 4 [3,13]; EDIT 7 [3,14]; EDIT 1 [3,15]; EDIT 4 [3,16]; EDIT 7 [3,17]; EDIT 1 [4,0]; EDIT 5 [4,1]; EDIT 9 [4,2]; EDIT 4 [4,3]; EDIT 8 [4,4]; EDIT 3 [4,5]; EDIT 7 [4,6]; EDIT 2 [4,7]; EDIT 6 [4,8]; EDIT 1 [4,9]; EDIT 5 [4,10]; EDIT 9 [4,11]; EDIT 4 [4,12]; EDIT 8 [4,13]; EDIT 3 [4,14]; EDIT 7 [4,15]; EDIT 2 [4,16]; EDIT 6 [4,17]; EDIT 1 [5,0]; EDIT 6 [5,1]; EDIT 2 [5,2]; EDIT 4 [5,6]; EDIT 9 [5,7]; EDIT 5 [5,8]; EDIT 1 [5,9]; EDIT 6 [5,10]; EDIT 2 [5,11]; EDIT 7 [5,12]; EDIT 9 [5,16]; EDIT 5 [5,17]; EDIT 1 [6,0]; EDIT 7 [6,1]; EDIT 4 [6,2]; EDIT 1 [6,6]; EDIT 7 [6,7]; EDIT 4 [6,8]; EDIT 7 [6,16]; EDIT 4 [6,17]; EDIT 1 [7,0]; EDIT 8 [7,1]; EDIT 6 [7,2]; EDIT 7 [7,6]; EDIT 5 [7,7]; EDIT 3 [7,8]; EDIT 2 [7,13]; EDIT 9 [7,14]; EDIT 7 [7,15]; EDIT 5 [7,16]; EDIT 3 [7,17]; EDIT 1 [8,0]; EDIT 9 [8,1]; EDIT 8 [8,2]; EDIT 4 [8,6]; EDIT 3 [8,7]; EDIT 2 [8,8]; EDIT 6 [8,13]; EDIT 5 [8,14]; EDIT 4 [8,15]; EDIT 3 [8,16]; EDIT 2 [8,17]; EDIT 1 [9,0]; EDIT 1 [9,1]; EDIT 1 [9,2]; EDIT 1 [9,3]; EDIT 1 [9,4]; EDIT 1 [9,5]; EDIT 1 [9,6]; EDIT 1 [9,7]; EDIT 1 [9,8]; EDIT 1 [9,9]; EDIT 1 [9,10]; EDIT 1 [9,11]; EDIT 1 [9,12]; EDIT 1 [9,13]; EDIT 1 [9,14]; EDIT 1 [9,15]; EDIT 1 [9,16]; EDIT 1 [9,17]; EDIT 1 [10,0]; EDIT 2 [10,1]; EDIT 3 [10,2]; EDIT 4 [10,3]; EDIT 5 [10,4]; EDIT 6 [10,5]; EDIT 7 [10,6]; EDIT 8 [10,7]; EDIT 9 [10,8]; EDIT 1 [10,9]; EDIT 2 [10,10]; EDIT 3 [10,11]; EDIT 4 [10,12]; EDIT 5 [10,13]; EDIT 6 [10,14]; EDIT 7 [10,15]; EDIT 8 [10,16]; EDIT 9 [10,17]; EDIT 1 [11,0]; EDIT 3 [11,1]; EDIT 5 [11,2]; EDIT 7 [11,3]; EDIT 9 [11,4]; EDIT 2 [11,5]; EDIT 4 [11,6]; EDIT 6 [11,7]; EDIT 8 [11,8]; EDIT 1 [11,9]; EDIT 3 [11,10]; EDIT 5 [11,11]; EDIT 7 [11,12]; EDIT 9 [11,13]; EDIT 2 [11,14]; EDIT 4 [11,15]; EDIT 6 [11,16]; EDIT 8 [11,17]; EDIT 1 [12,0]; EDIT 4 [12,1]; EDIT 7 [12,2]; EDIT 1 [12,3]; EDIT 4 [12,4]; EDIT 7 [12,5]; EDIT 1 [12,6]; EDIT 4 [12,7]; EDIT 7 [12,8]; EDIT 1 [12,9]; EDIT 4 [12,10]; EDIT 7 [12,11]; EDIT 1 [12,12]; EDIT 4 [12,13]; EDIT 7 [12,14]; EDIT 1 [12,15]; EDIT 4 [12,16]; EDIT 7 [12,17]; EDIT 1 [13,0]; EDIT 8 [13,4]; EDIT 3 [13,5]; EDIT 7 [13,6]; EDIT 2 [13,7]; EDIT 6 [13,8]; EDIT 1 [13,9]; EDIT 5 [13,10]; EDIT 9 [13,11]; EDIT 4 [13,12]; EDIT 8 [13,13]; EDIT 3 [13,14]; EDIT 7 [13,15]; EDIT 2 [13,16]; EDIT 6 [13,17]; EDIT 1 [14,0]; EDIT 3 [14,4]; EDIT 8 [14,5]; EDIT 4 [14,6]; EDIT 9 [14,7]; EDIT 5 [14,8]; EDIT 1 [14,9]; EDIT 6 [14,10]; EDIT 2 [14,11]; EDIT 7 [14,12]; EDIT 3 [14,13]; EDIT 8 [14,14]; EDIT 4 [14,15]; EDIT 9 [14,16]; EDIT 5 [14,17]; EDIT 1 [15,0]; EDIT 7 [15,4]; EDIT 4 [15,5]; EDIT 1 [15,6]; EDIT 7 [15,7]; EDIT 4 [15,8]; EDIT 1 [15,9]; EDIT 7 [15,10]; EDIT 4 [15,11]; EDIT 1 [15,12]; EDIT 7 [15,13]; EDIT 4 [15,14]; EDIT 1 [15,15]; EDIT 7 [15,16]; EDIT 4 [15,17]; EDIT 1 [16,0]; EDIT 2 [16,4]; EDIT 9 [16,5]; EDIT 7 [16,6]; EDIT 5 [16,7]; EDIT 3 [16,8]; EDIT 1 [16,9]; EDIT 8 [16,10]; EDIT 6 [16,11]; EDIT 4 [16,12]; EDIT 2 [16,13]; EDIT 9 [16,14]; EDIT 7 [16,15]; EDIT 5 [16,16]; EDIT 3 [16,17]; EDIT 1 [17,0]; EDIT 9 [17,1]; EDIT 8 [17,2]; EDIT 7 [17,3]; EDIT 6 [17,4]; EDIT 5 [17,5]; EDIT 4 [17,6]; EDIT 3 [17,7]; EDIT 2 [17,8]; EDIT 1 [17,9]; EDIT 9 [17,10]; EDIT 8 [17,11]; EDIT 7 [17,12]; EDIT 6 [17,13]; EDIT 5 [17,14]; EDIT 4 [17,15]; EDIT 3 [17,16]; EDIT 2 [17,17]}
COPY [9,9 17,17]
PASTE [0,9]
COPY [0,9 8,17]
PASTE [0,0]
COPY [0,0 8,8]
PASTE [9,0]
END