"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseAGIFiles = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
function parseAGIFiles(directory) {
    const agiFiles = fs_1.default.readdirSync(directory).filter(file => file.endsWith('.agi'));
    const knowledgeGraph = [];
    agiFiles.forEach(file => {
        const filePath = path_1.default.join(directory, file);
        const fileContent = fs_1.default.readFileSync(filePath, 'utf-8');
        const parsedData = extractRelevantInformation(fileContent, file);
        knowledgeGraph.push(parsedData);
    });
    return knowledgeGraph;
}
exports.parseAGIFiles = parseAGIFiles;
function extractRelevantInformation(content, fileName) {
    // Example: parse id from filename, label from first line, related from lines starting with 'related:'
    const lines = content.split('\n');
    const id = fileName.replace('.agi', '');
    const label = lines[0] || id;
    const related = lines.filter(l => l.startsWith('related:')).map(l => l.replace('related:', '').trim());
    return { id, label, related };
}
