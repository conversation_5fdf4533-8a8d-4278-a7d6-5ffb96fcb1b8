TRANSFERT {INIT 15x15; EDIT 6 [0,0]; EDIT 1 [0,1]; EDIT 1 [0,2]; EDIT 5 [0,3]; EDIT 1 [0,4]; EDIT 1 [0,5]; EDIT 1 [0,6]; EDIT 1 [0,7]; EDIT 1 [0,8]; EDIT 1 [0,9]; EDIT 1 [0,10]; EDIT 1 [0,11]; EDIT 1 [0,12]; EDIT 1 [0,13]; EDIT 1 [0,14]; EDIT 1 [1,0]; EDIT 4 [1,1]; EDIT 1 [1,2]; EDIT 1 [1,3]; EDIT 9 [1,4]; EDIT 1 [1,5]; EDIT 1 [1,6]; EDIT 1 [1,7]; EDIT 1 [1,8]; EDIT 5 [1,9]; EDIT 1 [1,10]; EDIT 1 [1,11]; EDIT 1 [1,12]; EDIT 1 [1,13]; EDIT 1 [1,14]; EDIT 5 [2,0]; EDIT 1 [2,1]; EDIT 1 [2,2]; EDIT 1 [2,3]; EDIT 4 [2,4]; EDIT 1 [2,5]; EDIT 1 [2,6]; EDIT 1 [2,7]; EDIT 1 [2,8]; EDIT 1 [2,9]; EDIT 1 [2,10]; EDIT 1 [2,11]; EDIT 1 [2,12]; EDIT 1 [2,13]; EDIT 1 [2,14]; EDIT 7 [3,0]; EDIT 2 [3,1]; EDIT 2 [3,2]; EDIT 2 [3,3]; EDIT 2 [3,4]; EDIT 6 [3,5]; EDIT 2 [3,6]; EDIT 9 [3,7]; EDIT 2 [3,8]; EDIT 2 [3,9]; EDIT 4 [3,10]; EDIT 2 [3,11]; EDIT 4 [3,12]; EDIT 2 [3,13]; EDIT 2 [3,14]; EDIT 2 [4,0]; EDIT 2 [4,1]; EDIT 9 [4,2]; EDIT 2 [4,3]; EDIT 1 [4,4]; EDIT 2 [4,5]; EDIT 2 [4,6]; EDIT 2 [4,7]; EDIT 3 [4,8]; EDIT 2 [4,9]; EDIT 2 [4,10]; EDIT 8 [4,11]; EDIT 2 [4,12]; EDIT 7 [4,13]; EDIT 2 [4,14]; EDIT 2 [5,0]; EDIT 5 [5,1]; EDIT 2 [5,2]; EDIT 2 [5,3]; EDIT 5 [5,4]; EDIT 6 [5,5]; EDIT 6 [5,6]; EDIT 2 [5,7]; EDIT 2 [5,8]; EDIT 2 [5,9]; EDIT 3 [5,10]; EDIT 2 [5,11]; EDIT 5 [5,12]; EDIT 2 [5,13]; EDIT 2 [5,14]; EDIT 2 [6,0]; EDIT 2 [6,1]; EDIT 2 [6,2]; EDIT 2 [6,3]; EDIT 2 [6,4]; EDIT 2 [6,5]; EDIT 2 [6,6]; EDIT 2 [6,7]; EDIT 2 [6,8]; EDIT 2 [6,9]; EDIT 6 [6,10]; EDIT 2 [6,11]; EDIT 8 [6,12]; EDIT 2 [6,13]; EDIT 2 [6,14]; EDIT 1 [7,0]; EDIT 8 [7,1]; EDIT 8 [7,2]; EDIT 8 [7,3]; EDIT 8 [7,4]; EDIT 8 [7,5]; EDIT 9 [7,6]; EDIT 8 [7,7]; EDIT 8 [7,8]; EDIT 8 [7,9]; EDIT 8 [7,10]; EDIT 8 [7,11]; EDIT 8 [7,12]; EDIT 8 [7,13]; EDIT 8 [7,14]; EDIT 8 [8,0]; EDIT 8 [8,1]; EDIT 8 [8,2]; EDIT 8 [8,3]; EDIT 1 [8,4]; EDIT 8 [8,5]; EDIT 8 [8,6]; EDIT 8 [8,7]; EDIT 8 [8,8]; EDIT 8 [8,9]; EDIT 7 [8,10]; EDIT 8 [8,11]; EDIT 8 [8,12]; EDIT 8 [8,13]; EDIT 9 [8,14]; EDIT 8 [9,0]; EDIT 8 [9,1]; EDIT 8 [9,2]; EDIT 8 [9,3]; EDIT 8 [9,4]; EDIT 8 [9,5]; EDIT 8 [9,6]; EDIT 8 [9,7]; EDIT 8 [9,8]; EDIT 8 [9,9]; EDIT 4 [9,10]; EDIT 8 [9,11]; EDIT 8 [9,12]; EDIT 8 [9,13]; EDIT 8 [9,14]; EDIT 8 [10,0]; EDIT 8 [10,1]; EDIT 8 [10,2]; EDIT 8 [10,3]; EDIT 8 [10,4]; EDIT 8 [10,5]; EDIT 8 [10,6]; EDIT 8 [10,7]; EDIT 5 [10,8]; EDIT 8 [10,9]; EDIT 8 [10,10]; EDIT 8 [10,11]; EDIT 1 [10,12]; EDIT 8 [10,13]; EDIT 8 [10,14]; EDIT 4 [11,0]; EDIT 4 [11,1]; EDIT 4 [11,2]; EDIT 4 [11,3]; EDIT 4 [11,4]; EDIT 4 [11,5]; EDIT 7 [11,6]; EDIT 3 [11,7]; EDIT 4 [11,8]; EDIT 4 [11,9]; EDIT 4 [11,10]; EDIT 4 [11,11]; EDIT 4 [11,12]; EDIT 2 [11,13]; EDIT 4 [11,14]; EDIT 4 [12,0]; EDIT 4 [12,1]; EDIT 7 [12,2]; EDIT 4 [12,3]; EDIT 4 [12,4]; EDIT 4 [12,5]; EDIT 4 [12,6]; EDIT 4 [12,7]; EDIT 4 [12,8]; EDIT 4 [12,9]; EDIT 8 [12,10]; EDIT 4 [12,11]; EDIT 4 [12,12]; EDIT 4 [12,13]; EDIT 4 [12,14]; EDIT 3 [13,0]; EDIT 3 [13,1]; EDIT 1 [13,2]; EDIT 9 [13,3]; EDIT 3 [13,4]; EDIT 3 [13,5]; EDIT 3 [13,6]; EDIT 3 [13,7]; EDIT 3 [13,8]; EDIT 3 [13,9]; EDIT 3 [13,10]; EDIT 3 [13,11]; EDIT 3 [13,12]; EDIT 3 [13,13]; EDIT 3 [13,14]; EDIT 8 [14,0]; EDIT 6 [14,1]; EDIT 3 [14,2]; EDIT 3 [14,3]; EDIT 8 [14,4]; EDIT 3 [14,5]; EDIT 3 [14,6]; EDIT 3 [14,7]; EDIT 3 [14,8]; EDIT 3 [14,9]; EDIT 3 [14,10]; EDIT 3 [14,11]; EDIT 3 [14,12]; EDIT 3 [14,13]; EDIT 3 [14,14]}
REPLACE 4,5,6,9 1 [0,0 2,14]
REPLACE 6,9,1,3,4,5,7,8 2 [3,0 6,14]
REPLACE 6,9,1,3,4,5,7 8 [7,0 10,14]
REPLACE 6,9,1,3,5,7,8,2 4 [11,0 12,14]
REPLACE 6,9,1,5,7,8,2 3 [13,0 14,14]
END