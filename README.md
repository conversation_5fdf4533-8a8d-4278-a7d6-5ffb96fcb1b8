# arc-knowledge-graph

## Overview

The arc-knowledge-graph project is designed to generate a knowledge graph from AGI files located in the `arcdata/training` directory. This project utilizes TypeScript for type safety and modularity.

## Project Structure

```
arc-knowledge-graph
├── src
│   ├── generateGraph.ts        # Responsible for generating the knowledge graph
│   └── utils
│       └── parseAGI.ts         # Contains functions to parse AGI files
├── arcdata
│   └── training                 # Directory containing AGI files
├── output
│   └── knowledge-graph.json     # Output file for the generated knowledge graph
├── package.json                 # npm configuration file
├── tsconfig.json                # TypeScript configuration file
└── README.md                    # Project documentation
```

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm (Node package manager)

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd arc-knowledge-graph
   ```

2. Install the dependencies:
   ```
   npm install
   ```

### Usage

To generate the knowledge graph from the AGI files, run the following command:

```
npm run generate
```

This command will execute the `generateGraph.ts` script, which will read the AGI files from the `arcdata/training` directory, parse them using the utility functions in `parseAGI.ts`, and output the resulting knowledge graph to `output/knowledge-graph.json`.

### Output

The generated knowledge graph will be saved in JSON format in the `output/knowledge-graph.json` file. You can open this file to view the structure and relationships extracted from the AGI files.

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue for any suggestions or improvements.

## License

This project is licensed under the MIT License. See the LICENSE file for more details.