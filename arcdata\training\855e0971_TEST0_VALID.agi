TRANSFERT {INIT 15x17; EDIT 8 [0,0]; EDIT 8 [0,1]; EDIT 8 [0,2]; EDIT 8 [0,3]; EDIT 8 [0,5]; EDIT 8 [0,6]; EDIT 8 [0,7]; EDIT 8 [0,8]; EDIT 8 [0,9]; EDIT 8 [0,10]; EDIT 8 [0,11]; EDIT 8 [0,12]; EDIT 8 [0,13]; EDIT 8 [0,14]; EDIT 8 [1,0]; EDIT 8 [1,1]; EDIT 8 [1,2]; EDIT 8 [1,3]; EDIT 8 [1,4]; EDIT 8 [1,5]; EDIT 8 [1,6]; EDIT 8 [1,7]; EDIT 8 [1,8]; EDIT 8 [1,9]; EDIT 8 [1,10]; EDIT 8 [1,11]; EDIT 8 [1,12]; EDIT 8 [1,13]; EDIT 8 [1,14]; EDIT 8 [2,0]; EDIT 8 [2,1]; EDIT 8 [2,2]; EDIT 8 [2,3]; EDIT 8 [2,4]; EDIT 8 [2,5]; EDIT 8 [2,6]; EDIT 8 [2,7]; EDIT 8 [2,8]; EDIT 8 [2,9]; EDIT 8 [2,10]; EDIT 8 [2,11]; EDIT 8 [2,13]; EDIT 8 [2,14]; EDIT 8 [3,0]; EDIT 8 [3,1]; EDIT 8 [3,2]; EDIT 8 [3,3]; EDIT 8 [3,4]; EDIT 8 [3,5]; EDIT 8 [3,6]; EDIT 8 [3,7]; EDIT 8 [3,8]; EDIT 8 [3,9]; EDIT 8 [3,10]; EDIT 8 [3,11]; EDIT 8 [3,12]; EDIT 8 [3,13]; EDIT 8 [3,14]; EDIT 1 [4,0]; EDIT 1 [4,1]; EDIT 1 [4,2]; EDIT 1 [4,3]; EDIT 1 [4,4]; EDIT 1 [4,5]; EDIT 1 [4,6]; EDIT 1 [4,7]; EDIT 1 [4,8]; EDIT 1 [4,9]; EDIT 1 [4,10]; EDIT 1 [4,11]; EDIT 1 [4,12]; EDIT 1 [4,13]; EDIT 1 [4,14]; EDIT 1 [5,0]; EDIT 1 [5,1]; EDIT 1 [5,2]; EDIT 1 [5,3]; EDIT 1 [5,4]; EDIT 1 [5,5]; EDIT 1 [5,6]; EDIT 1 [5,7]; EDIT 1 [5,8]; EDIT 1 [5,9]; EDIT 1 [5,10]; EDIT 1 [5,11]; EDIT 1 [5,12]; EDIT 1 [5,13]; EDIT 1 [5,14]; EDIT 1 [6,0]; EDIT 1 [6,1]; EDIT 1 [6,2]; EDIT 1 [6,3]; EDIT 1 [6,4]; EDIT 1 [6,5]; EDIT 1 [6,7]; EDIT 1 [6,8]; EDIT 1 [6,9]; EDIT 1 [6,10]; EDIT 1 [6,11]; EDIT 1 [6,12]; EDIT 1 [6,13]; EDIT 1 [6,14]; EDIT 1 [7,0]; EDIT 1 [7,1]; EDIT 1 [7,2]; EDIT 1 [7,3]; EDIT 1 [7,4]; EDIT 1 [7,5]; EDIT 1 [7,6]; EDIT 1 [7,7]; EDIT 1 [7,8]; EDIT 1 [7,9]; EDIT 1 [7,10]; EDIT 1 [7,11]; EDIT 1 [7,12]; EDIT 1 [7,13]; EDIT 1 [7,14]; EDIT 4 [8,0]; EDIT 4 [8,1]; EDIT 4 [8,2]; EDIT 4 [8,3]; EDIT 4 [8,4]; EDIT 4 [8,5]; EDIT 4 [8,6]; EDIT 4 [8,7]; EDIT 4 [8,8]; EDIT 4 [8,9]; EDIT 4 [8,10]; EDIT 4 [8,11]; EDIT 4 [8,12]; EDIT 4 [8,13]; EDIT 4 [8,14]; EDIT 4 [9,0]; EDIT 4 [9,1]; EDIT 4 [9,2]; EDIT 4 [9,3]; EDIT 4 [9,4]; EDIT 4 [9,5]; EDIT 4 [9,6]; EDIT 4 [9,7]; EDIT 4 [9,8]; EDIT 4 [9,9]; EDIT 4 [9,10]; EDIT 4 [9,11]; EDIT 4 [9,12]; EDIT 4 [9,13]; EDIT 4 [9,14]; EDIT 4 [10,0]; EDIT 4 [10,1]; EDIT 4 [10,2]; EDIT 4 [10,3]; EDIT 4 [10,4]; EDIT 4 [10,5]; EDIT 4 [10,6]; EDIT 4 [10,7]; EDIT 4 [10,8]; EDIT 4 [10,9]; EDIT 4 [10,11]; EDIT 4 [10,12]; EDIT 4 [10,13]; EDIT 4 [10,14]; EDIT 4 [11,0]; EDIT 4 [11,1]; EDIT 4 [11,2]; EDIT 4 [11,3]; EDIT 4 [11,4]; EDIT 4 [11,5]; EDIT 4 [11,6]; EDIT 4 [11,7]; EDIT 4 [11,8]; EDIT 4 [11,9]; EDIT 4 [11,10]; EDIT 4 [11,11]; EDIT 4 [11,12]; EDIT 4 [11,13]; EDIT 4 [11,14]; EDIT 4 [12,0]; EDIT 4 [12,1]; EDIT 4 [12,2]; EDIT 4 [12,3]; EDIT 4 [12,4]; EDIT 4 [12,5]; EDIT 4 [12,6]; EDIT 4 [12,7]; EDIT 4 [12,8]; EDIT 4 [12,9]; EDIT 4 [12,10]; EDIT 4 [12,11]; EDIT 4 [12,12]; EDIT 4 [12,13]; EDIT 4 [12,14]; EDIT 2 [13,0]; EDIT 2 [13,1]; EDIT 2 [13,2]; EDIT 2 [13,3]; EDIT 2 [13,4]; EDIT 2 [13,5]; EDIT 2 [13,6]; EDIT 2 [13,7]; EDIT 2 [13,8]; EDIT 2 [13,9]; EDIT 2 [13,10]; EDIT 2 [13,11]; EDIT 2 [13,12]; EDIT 2 [13,13]; EDIT 2 [13,14]; EDIT 2 [14,0]; EDIT 2 [14,1]; EDIT 2 [14,2]; EDIT 2 [14,3]; EDIT 2 [14,4]; EDIT 2 [14,5]; EDIT 2 [14,6]; EDIT 2 [14,7]; EDIT 2 [14,8]; EDIT 2 [14,9]; EDIT 2 [14,10]; EDIT 2 [14,11]; EDIT 2 [14,12]; EDIT 2 [14,13]; EDIT 2 [14,14]; EDIT 2 [15,0]; EDIT 2 [15,2]; EDIT 2 [15,3]; EDIT 2 [15,4]; EDIT 2 [15,5]; EDIT 2 [15,6]; EDIT 2 [15,7]; EDIT 2 [15,8]; EDIT 2 [15,9]; EDIT 2 [15,10]; EDIT 2 [15,11]; EDIT 2 [15,12]; EDIT 2 [15,13]; EDIT 2 [15,14]; EDIT 2 [16,0]; EDIT 2 [16,1]; EDIT 2 [16,2]; EDIT 2 [16,3]; EDIT 2 [16,4]; EDIT 2 [16,5]; EDIT 2 [16,6]; EDIT 2 [16,7]; EDIT 2 [16,8]; EDIT 2 [16,9]; EDIT 2 [16,10]; EDIT 2 [16,11]; EDIT 2 [16,12]; EDIT 2 [16,13]; EDIT 2 [16,14]}
EDITS {EDIT 0 [1,4]; EDIT 0 [2,4]; EDIT 0 [3,4]; EDIT 0 [0,12]; EDIT 0 [1,12]; EDIT 0 [3,12]; EDIT 0 [4,6]; EDIT 0 [5,6]; EDIT 0 [7,6]; EDIT 0 [8,10]; EDIT 0 [9,10]; EDIT 0 [11,10]; EDIT 0 [12,10]; EDIT 0 [13,1]; EDIT 0 [14,1]; EDIT 0 [16,1]}
END